{"ast": null, "code": "import { isSVGComponent } from './is-svg-component.mjs';\nimport { createUseRender } from '../use-render.mjs';\nimport { svgMotionConfig } from '../../svg/config-motion.mjs';\nimport { htmlMotionConfig } from '../../html/config-motion.mjs';\nfunction createDomMotionConfig(Component, _ref, preloadedFeatures, createVisualElement) {\n  let {\n    forwardMotionProps = false\n  } = _ref;\n  const baseConfig = isSVGComponent(Component) ? svgMotionConfig : htmlMotionConfig;\n  return {\n    ...baseConfig,\n    preloadedFeatures,\n    useRender: createUseRender(forwardMotionProps),\n    createVisualElement,\n    Component\n  };\n}\nexport { createDomMotionConfig };", "map": {"version": 3, "names": ["isSVGComponent", "createUseRender", "svgMotionConfig", "htmlMotionConfig", "createDomMotionConfig", "Component", "_ref", "preloadedFeatures", "createVisualElement", "forwardMotionProps", "baseConfig", "useRender"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/render/dom/utils/create-config.mjs"], "sourcesContent": ["import { isSVGComponent } from './is-svg-component.mjs';\nimport { createUseRender } from '../use-render.mjs';\nimport { svgMotionConfig } from '../../svg/config-motion.mjs';\nimport { htmlMotionConfig } from '../../html/config-motion.mjs';\n\nfunction createDomMotionConfig(Component, { forwardMotionProps = false }, preloadedFeatures, createVisualElement) {\n    const baseConfig = isSVGComponent(Component)\n        ? svgMotionConfig\n        : htmlMotionConfig;\n    return {\n        ...baseConfig,\n        preloadedFeatures,\n        useRender: createUseRender(forwardMotionProps),\n        createVisualElement,\n        Component,\n    };\n}\n\nexport { createDomMotionConfig };\n"], "mappings": "AAAA,SAASA,cAAc,QAAQ,wBAAwB;AACvD,SAASC,eAAe,QAAQ,mBAAmB;AACnD,SAASC,eAAe,QAAQ,6BAA6B;AAC7D,SAASC,gBAAgB,QAAQ,8BAA8B;AAE/D,SAASC,qBAAqBA,CAACC,SAAS,EAAAC,IAAA,EAAkCC,iBAAiB,EAAEC,mBAAmB,EAAE;EAAA,IAAxE;IAAEC,kBAAkB,GAAG;EAAM,CAAC,GAAAH,IAAA;EACpE,MAAMI,UAAU,GAAGV,cAAc,CAACK,SAAS,CAAC,GACtCH,eAAe,GACfC,gBAAgB;EACtB,OAAO;IACH,GAAGO,UAAU;IACbH,iBAAiB;IACjBI,SAAS,EAAEV,eAAe,CAACQ,kBAAkB,CAAC;IAC9CD,mBAAmB;IACnBH;EACJ,CAAC;AACL;AAEA,SAASD,qBAAqB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}