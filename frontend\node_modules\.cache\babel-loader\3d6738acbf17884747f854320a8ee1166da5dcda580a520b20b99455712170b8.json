{"ast": null, "code": "import { warning } from '../../utils/errors.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { createAcceleratedAnimation } from '../animators/waapi/create-accelerated-animation.mjs';\nimport { createInstantAnimation } from '../animators/instant.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isAnimatable } from '../utils/is-animatable.mjs';\nimport { getKeyframes } from '../utils/keyframes.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { animateValue } from '../animators/js/index.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\nconst animateMotionValue = function (valueName, value, target) {\n  let transition = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : {};\n  return onComplete => {\n    const valueTransition = getValueTransition(transition, valueName) || {};\n    /**\n     * Most transition values are currently completely overwritten by value-specific\n     * transitions. In the future it'd be nicer to blend these transitions. But for now\n     * delay actually does inherit from the root transition if not value-specific.\n     */\n    const delay = valueTransition.delay || transition.delay || 0;\n    /**\n     * Elapsed isn't a public transition option but can be passed through from\n     * optimized appear effects in milliseconds.\n     */\n    let {\n      elapsed = 0\n    } = transition;\n    elapsed = elapsed - secondsToMilliseconds(delay);\n    const keyframes = getKeyframes(value, valueName, target, valueTransition);\n    /**\n     * Check if we're able to animate between the start and end keyframes,\n     * and throw a warning if we're attempting to animate between one that's\n     * animatable and another that isn't.\n     */\n    const originKeyframe = keyframes[0];\n    const targetKeyframe = keyframes[keyframes.length - 1];\n    const isOriginAnimatable = isAnimatable(valueName, originKeyframe);\n    const isTargetAnimatable = isAnimatable(valueName, targetKeyframe);\n    warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${valueName} from \"${originKeyframe}\" to \"${targetKeyframe}\". ${originKeyframe} is not an animatable value - to enable this animation set ${originKeyframe} to a value animatable to ${targetKeyframe} via the \\`style\\` property.`);\n    let options = {\n      keyframes,\n      velocity: value.getVelocity(),\n      ease: \"easeOut\",\n      ...valueTransition,\n      delay: -elapsed,\n      onUpdate: v => {\n        value.set(v);\n        valueTransition.onUpdate && valueTransition.onUpdate(v);\n      },\n      onComplete: () => {\n        onComplete();\n        valueTransition.onComplete && valueTransition.onComplete();\n      }\n    };\n    /**\n     * If there's no transition defined for this value, we can generate\n     * unqiue transition settings for this value.\n     */\n    if (!isTransitionDefined(valueTransition)) {\n      options = {\n        ...options,\n        ...getDefaultTransition(valueName, options)\n      };\n    }\n    /**\n     * Both WAAPI and our internal animation functions use durations\n     * as defined by milliseconds, while our external API defines them\n     * as seconds.\n     */\n    if (options.duration) {\n      options.duration = secondsToMilliseconds(options.duration);\n    }\n    if (options.repeatDelay) {\n      options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n    }\n    if (!isOriginAnimatable || !isTargetAnimatable || instantAnimationState.current || valueTransition.type === false || MotionGlobalConfig.skipAnimations) {\n      /**\n       * If we can't animate this value, or the global instant animation flag is set,\n       * or this is simply defined as an instant transition, return an instant transition.\n       */\n      return createInstantAnimation(instantAnimationState.current ? {\n        ...options,\n        delay: 0\n      } : options);\n    }\n    /**\n     * Animate via WAAPI if possible.\n     */\n    if (\n    /**\n     * If this is a handoff animation, the optimised animation will be running via\n     * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n     * optimised animation.\n     */\n    !transition.isHandoff && value.owner && value.owner.current instanceof HTMLElement &&\n    /**\n     * If we're outputting values to onUpdate then we can't use WAAPI as there's\n     * no way to read the value from WAAPI every frame.\n     */\n    !value.owner.getProps().onUpdate) {\n      const acceleratedAnimation = createAcceleratedAnimation(value, valueName, options);\n      if (acceleratedAnimation) return acceleratedAnimation;\n    }\n    /**\n     * If we didn't create an accelerated animation, create a JS animation\n     */\n    return animateValue(options);\n  };\n};\nexport { animateMotionValue };", "map": {"version": 3, "names": ["warning", "secondsToMilliseconds", "instantAnimationState", "createAcceleratedAnimation", "createInstantAnimation", "getDefaultTransition", "isAnimatable", "getKeyframes", "getValueTransition", "isTransitionDefined", "animateValue", "MotionGlobalConfig", "animateMotionValue", "valueName", "value", "target", "transition", "arguments", "length", "undefined", "onComplete", "valueTransition", "delay", "elapsed", "keyframes", "originKeyframe", "targetKeyframe", "isOriginAnimatable", "isTargetAnimatable", "options", "velocity", "getVelocity", "ease", "onUpdate", "v", "set", "duration", "repeatDelay", "current", "type", "skipAnimations", "<PERSON><PERSON><PERSON><PERSON>", "owner", "HTMLElement", "getProps", "acceleratedAnimation"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/animation/interfaces/motion-value.mjs"], "sourcesContent": ["import { warning } from '../../utils/errors.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { instantAnimationState } from '../../utils/use-instant-transition-state.mjs';\nimport { createAcceleratedAnimation } from '../animators/waapi/create-accelerated-animation.mjs';\nimport { createInstantAnimation } from '../animators/instant.mjs';\nimport { getDefaultTransition } from '../utils/default-transitions.mjs';\nimport { isAnimatable } from '../utils/is-animatable.mjs';\nimport { getKeyframes } from '../utils/keyframes.mjs';\nimport { getValueTransition, isTransitionDefined } from '../utils/transitions.mjs';\nimport { animateValue } from '../animators/js/index.mjs';\nimport { MotionGlobalConfig } from '../../utils/GlobalConfig.mjs';\n\nconst animateMotionValue = (valueName, value, target, transition = {}) => {\n    return (onComplete) => {\n        const valueTransition = getValueTransition(transition, valueName) || {};\n        /**\n         * Most transition values are currently completely overwritten by value-specific\n         * transitions. In the future it'd be nicer to blend these transitions. But for now\n         * delay actually does inherit from the root transition if not value-specific.\n         */\n        const delay = valueTransition.delay || transition.delay || 0;\n        /**\n         * Elapsed isn't a public transition option but can be passed through from\n         * optimized appear effects in milliseconds.\n         */\n        let { elapsed = 0 } = transition;\n        elapsed = elapsed - secondsToMilliseconds(delay);\n        const keyframes = getKeyframes(value, valueName, target, valueTransition);\n        /**\n         * Check if we're able to animate between the start and end keyframes,\n         * and throw a warning if we're attempting to animate between one that's\n         * animatable and another that isn't.\n         */\n        const originKeyframe = keyframes[0];\n        const targetKeyframe = keyframes[keyframes.length - 1];\n        const isOriginAnimatable = isAnimatable(valueName, originKeyframe);\n        const isTargetAnimatable = isAnimatable(valueName, targetKeyframe);\n        warning(isOriginAnimatable === isTargetAnimatable, `You are trying to animate ${valueName} from \"${originKeyframe}\" to \"${targetKeyframe}\". ${originKeyframe} is not an animatable value - to enable this animation set ${originKeyframe} to a value animatable to ${targetKeyframe} via the \\`style\\` property.`);\n        let options = {\n            keyframes,\n            velocity: value.getVelocity(),\n            ease: \"easeOut\",\n            ...valueTransition,\n            delay: -elapsed,\n            onUpdate: (v) => {\n                value.set(v);\n                valueTransition.onUpdate && valueTransition.onUpdate(v);\n            },\n            onComplete: () => {\n                onComplete();\n                valueTransition.onComplete && valueTransition.onComplete();\n            },\n        };\n        /**\n         * If there's no transition defined for this value, we can generate\n         * unqiue transition settings for this value.\n         */\n        if (!isTransitionDefined(valueTransition)) {\n            options = {\n                ...options,\n                ...getDefaultTransition(valueName, options),\n            };\n        }\n        /**\n         * Both WAAPI and our internal animation functions use durations\n         * as defined by milliseconds, while our external API defines them\n         * as seconds.\n         */\n        if (options.duration) {\n            options.duration = secondsToMilliseconds(options.duration);\n        }\n        if (options.repeatDelay) {\n            options.repeatDelay = secondsToMilliseconds(options.repeatDelay);\n        }\n        if (!isOriginAnimatable ||\n            !isTargetAnimatable ||\n            instantAnimationState.current ||\n            valueTransition.type === false ||\n            MotionGlobalConfig.skipAnimations) {\n            /**\n             * If we can't animate this value, or the global instant animation flag is set,\n             * or this is simply defined as an instant transition, return an instant transition.\n             */\n            return createInstantAnimation(instantAnimationState.current\n                ? { ...options, delay: 0 }\n                : options);\n        }\n        /**\n         * Animate via WAAPI if possible.\n         */\n        if (\n        /**\n         * If this is a handoff animation, the optimised animation will be running via\n         * WAAPI. Therefore, this animation must be JS to ensure it runs \"under\" the\n         * optimised animation.\n         */\n        !transition.isHandoff &&\n            value.owner &&\n            value.owner.current instanceof HTMLElement &&\n            /**\n             * If we're outputting values to onUpdate then we can't use WAAPI as there's\n             * no way to read the value from WAAPI every frame.\n             */\n            !value.owner.getProps().onUpdate) {\n            const acceleratedAnimation = createAcceleratedAnimation(value, valueName, options);\n            if (acceleratedAnimation)\n                return acceleratedAnimation;\n        }\n        /**\n         * If we didn't create an accelerated animation, create a JS animation\n         */\n        return animateValue(options);\n    };\n};\n\nexport { animateMotionValue };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,wBAAwB;AAChD,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,qBAAqB,QAAQ,8CAA8C;AACpF,SAASC,0BAA0B,QAAQ,qDAAqD;AAChG,SAASC,sBAAsB,QAAQ,0BAA0B;AACjE,SAASC,oBAAoB,QAAQ,kCAAkC;AACvE,SAASC,YAAY,QAAQ,4BAA4B;AACzD,SAASC,YAAY,QAAQ,wBAAwB;AACrD,SAASC,kBAAkB,EAAEC,mBAAmB,QAAQ,0BAA0B;AAClF,SAASC,YAAY,QAAQ,2BAA2B;AACxD,SAASC,kBAAkB,QAAQ,8BAA8B;AAEjE,MAAMC,kBAAkB,GAAG,SAAAA,CAACC,SAAS,EAAEC,KAAK,EAAEC,MAAM,EAAsB;EAAA,IAApBC,UAAU,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EACjE,OAAQG,UAAU,IAAK;IACnB,MAAMC,eAAe,GAAGb,kBAAkB,CAACQ,UAAU,EAAEH,SAAS,CAAC,IAAI,CAAC,CAAC;IACvE;AACR;AACA;AACA;AACA;IACQ,MAAMS,KAAK,GAAGD,eAAe,CAACC,KAAK,IAAIN,UAAU,CAACM,KAAK,IAAI,CAAC;IAC5D;AACR;AACA;AACA;IACQ,IAAI;MAAEC,OAAO,GAAG;IAAE,CAAC,GAAGP,UAAU;IAChCO,OAAO,GAAGA,OAAO,GAAGtB,qBAAqB,CAACqB,KAAK,CAAC;IAChD,MAAME,SAAS,GAAGjB,YAAY,CAACO,KAAK,EAAED,SAAS,EAAEE,MAAM,EAAEM,eAAe,CAAC;IACzE;AACR;AACA;AACA;AACA;IACQ,MAAMI,cAAc,GAAGD,SAAS,CAAC,CAAC,CAAC;IACnC,MAAME,cAAc,GAAGF,SAAS,CAACA,SAAS,CAACN,MAAM,GAAG,CAAC,CAAC;IACtD,MAAMS,kBAAkB,GAAGrB,YAAY,CAACO,SAAS,EAAEY,cAAc,CAAC;IAClE,MAAMG,kBAAkB,GAAGtB,YAAY,CAACO,SAAS,EAAEa,cAAc,CAAC;IAClE1B,OAAO,CAAC2B,kBAAkB,KAAKC,kBAAkB,EAAG,6BAA4Bf,SAAU,UAASY,cAAe,SAAQC,cAAe,MAAKD,cAAe,8DAA6DA,cAAe,6BAA4BC,cAAe,8BAA6B,CAAC;IAClT,IAAIG,OAAO,GAAG;MACVL,SAAS;MACTM,QAAQ,EAAEhB,KAAK,CAACiB,WAAW,EAAE;MAC7BC,IAAI,EAAE,SAAS;MACf,GAAGX,eAAe;MAClBC,KAAK,EAAE,CAACC,OAAO;MACfU,QAAQ,EAAGC,CAAC,IAAK;QACbpB,KAAK,CAACqB,GAAG,CAACD,CAAC,CAAC;QACZb,eAAe,CAACY,QAAQ,IAAIZ,eAAe,CAACY,QAAQ,CAACC,CAAC,CAAC;MAC3D,CAAC;MACDd,UAAU,EAAEA,CAAA,KAAM;QACdA,UAAU,EAAE;QACZC,eAAe,CAACD,UAAU,IAAIC,eAAe,CAACD,UAAU,EAAE;MAC9D;IACJ,CAAC;IACD;AACR;AACA;AACA;IACQ,IAAI,CAACX,mBAAmB,CAACY,eAAe,CAAC,EAAE;MACvCQ,OAAO,GAAG;QACN,GAAGA,OAAO;QACV,GAAGxB,oBAAoB,CAACQ,SAAS,EAAEgB,OAAO;MAC9C,CAAC;IACL;IACA;AACR;AACA;AACA;AACA;IACQ,IAAIA,OAAO,CAACO,QAAQ,EAAE;MAClBP,OAAO,CAACO,QAAQ,GAAGnC,qBAAqB,CAAC4B,OAAO,CAACO,QAAQ,CAAC;IAC9D;IACA,IAAIP,OAAO,CAACQ,WAAW,EAAE;MACrBR,OAAO,CAACQ,WAAW,GAAGpC,qBAAqB,CAAC4B,OAAO,CAACQ,WAAW,CAAC;IACpE;IACA,IAAI,CAACV,kBAAkB,IACnB,CAACC,kBAAkB,IACnB1B,qBAAqB,CAACoC,OAAO,IAC7BjB,eAAe,CAACkB,IAAI,KAAK,KAAK,IAC9B5B,kBAAkB,CAAC6B,cAAc,EAAE;MACnC;AACZ;AACA;AACA;MACY,OAAOpC,sBAAsB,CAACF,qBAAqB,CAACoC,OAAO,GACrD;QAAE,GAAGT,OAAO;QAAEP,KAAK,EAAE;MAAE,CAAC,GACxBO,OAAO,CAAC;IAClB;IACA;AACR;AACA;IACQ;IACA;AACR;AACA;AACA;AACA;IACQ,CAACb,UAAU,CAACyB,SAAS,IACjB3B,KAAK,CAAC4B,KAAK,IACX5B,KAAK,CAAC4B,KAAK,CAACJ,OAAO,YAAYK,WAAW;IAC1C;AACZ;AACA;AACA;IACY,CAAC7B,KAAK,CAAC4B,KAAK,CAACE,QAAQ,EAAE,CAACX,QAAQ,EAAE;MAClC,MAAMY,oBAAoB,GAAG1C,0BAA0B,CAACW,KAAK,EAAED,SAAS,EAAEgB,OAAO,CAAC;MAClF,IAAIgB,oBAAoB,EACpB,OAAOA,oBAAoB;IACnC;IACA;AACR;AACA;IACQ,OAAOnC,YAAY,CAACmB,OAAO,CAAC;EAChC,CAAC;AACL,CAAC;AAED,SAASjB,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}