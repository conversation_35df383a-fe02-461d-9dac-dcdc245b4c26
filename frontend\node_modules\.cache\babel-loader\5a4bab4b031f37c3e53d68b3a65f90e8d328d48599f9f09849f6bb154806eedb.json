{"ast": null, "code": "var _jsxFileName = \"D:\\\\MINHNGUYET\\\\NAM4\\\\DoAn\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\searchBox.jsx\",\n  _s = $RefreshSig$();\n// import React, { useEffect, useState } from \"react\";\n// import { Form } from \"react-bootstrap\";\n// import { useNavigate } from 'react-router-dom';\n\n// function SearchBox({ keyword, setKeyword }) {\n//   const [keywordText, setKeywordText] = useState(keyword);\n//   const navigate = useNavigate();\n\n//   const queryParams = new URLSearchParams(window.location.search);\n//   const brandParam = queryParams.get(\"brand\")\n//     ? Number(queryParams.get(\"brand\"))\n//     : 0;\n//   const categoryParam = queryParams.get(\"category\")\n//     ? Number(queryParams.get(\"category\"))\n//     : 0;\n\n//   const handleSubmit = (e) => {\n//     e.preventDefault();\n//     navigate(`/search?keyword=${keywordText}&brand=${brandParam}&category=${categoryParam}`);\n//     setKeyword(keywordText);\n//   };\n\n//   return (\n//     <Form onSubmit={handleSubmit} className=\"d-flex search-form position-relative\">\n//       <Form.Control\n//         type=\"text\"\n//         placeholder=\"Tìm sản phẩm...\"\n//         value={keywordText}\n//         onChange={(e) => setKeywordText(e.currentTarget.value)}\n//         className=\"me-2 search-input-with-icon\"\n//       />\n//       <button type=\"submit\" className=\"search-icon-btn\">\n//         <i className=\"fas fa-search\"></i>\n//       </button>\n//     </Form>\n//   );\n// }\n\n// export default SearchBox;\nimport React, { useEffect, useState } from \"react\";\nimport { useNavigate } from 'react-router-dom';\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction SearchBox(_ref) {\n  _s();\n  let {\n    keyword,\n    setKeyword\n  } = _ref;\n  const [keywordText, setKeywordText] = useState(keyword);\n  const navigate = useNavigate();\n  const queryParams = new URLSearchParams(window.location.search);\n  const brandParam = queryParams.get(\"brand\") ? Number(queryParams.get(\"brand\")) : 0;\n  const categoryParam = queryParams.get(\"category\") ? Number(queryParams.get(\"category\")) : 0;\n  const handleSubmit = e => {\n    e.preventDefault();\n    navigate(`/search?keyword=${keywordText}&brand=${brandParam}&category=${categoryParam}`);\n    setKeyword(keywordText);\n  };\n  return /*#__PURE__*/_jsxDEV(\"form\", {\n    onSubmit: handleSubmit,\n    className: \"d-flex search-form position-relative\",\n    children: [/*#__PURE__*/_jsxDEV(\"input\", {\n      type: \"text\",\n      placeholder: \"T\\xECm s\\u1EA3n ph\\u1EA9m...\",\n      value: keywordText,\n      onChange: e => setKeywordText(e.currentTarget.value),\n      className: \"search-input-with-icon\"\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 63,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n      type: \"submit\",\n      className: \"search-icon-btn\",\n      children: /*#__PURE__*/_jsxDEV(\"i\", {\n        className: \"fas fa-search\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 71,\n        columnNumber: 9\n      }, this)\n    }, void 0, false, {\n      fileName: _jsxFileName,\n      lineNumber: 70,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 62,\n    columnNumber: 5\n  }, this);\n}\n_s(SearchBox, \"zTcO3TE4lf0HlL24i5XJi/eY8z0=\", false, function () {\n  return [useNavigate];\n});\n_c = SearchBox;\nexport default SearchBox;\nvar _c;\n$RefreshReg$(_c, \"SearchBox\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useNavigate", "jsxDEV", "_jsxDEV", "SearchBox", "_ref", "_s", "keyword", "setKeyword", "keywordText", "setKeywordText", "navigate", "queryParams", "URLSearchParams", "window", "location", "search", "brandParam", "get", "Number", "categoryParam", "handleSubmit", "e", "preventDefault", "onSubmit", "className", "children", "type", "placeholder", "value", "onChange", "currentTarget", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "_c", "$RefreshReg$"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/src/components/searchBox.jsx"], "sourcesContent": ["// import React, { useEffect, useState } from \"react\";\n// import { Form } from \"react-bootstrap\";\n// import { useNavigate } from 'react-router-dom';\n\n// function SearchBox({ keyword, setKeyword }) {\n//   const [keywordText, setKeywordText] = useState(keyword);\n//   const navigate = useNavigate();\n\n//   const queryParams = new URLSearchParams(window.location.search);\n//   const brandParam = queryParams.get(\"brand\")\n//     ? Number(queryParams.get(\"brand\"))\n//     : 0;\n//   const categoryParam = queryParams.get(\"category\")\n//     ? Number(queryParams.get(\"category\"))\n//     : 0;\n\n//   const handleSubmit = (e) => {\n//     e.preventDefault();\n//     navigate(`/search?keyword=${keywordText}&brand=${brandParam}&category=${categoryParam}`);\n//     setKeyword(keywordText);\n//   };\n\n//   return (\n//     <Form onSubmit={handleSubmit} className=\"d-flex search-form position-relative\">\n//       <Form.Control\n//         type=\"text\"\n//         placeholder=\"Tìm sản phẩm...\"\n//         value={keywordText}\n//         onChange={(e) => setKeywordText(e.currentTarget.value)}\n//         className=\"me-2 search-input-with-icon\"\n//       />\n//       <button type=\"submit\" className=\"search-icon-btn\">\n//         <i className=\"fas fa-search\"></i>\n//       </button>\n//     </Form>\n//   );\n// }\n\n// export default SearchBox;\nimport React, { useEffect, useState } from \"react\";\nimport { useNavigate } from 'react-router-dom';\n\nfunction SearchBox({ keyword, setKeyword }) {\n  const [keywordText, setKeywordText] = useState(keyword);\n  const navigate = useNavigate();\n\n  const queryParams = new URLSearchParams(window.location.search);\n  const brandParam = queryParams.get(\"brand\")\n    ? Number(queryParams.get(\"brand\"))\n    : 0;\n  const categoryParam = queryParams.get(\"category\")\n    ? Number(queryParams.get(\"category\"))\n    : 0;\n\n  const handleSubmit = (e) => {\n    e.preventDefault();\n    navigate(`/search?keyword=${keywordText}&brand=${brandParam}&category=${categoryParam}`);\n    setKeyword(keywordText);\n  };\n\n  return (\n    <form onSubmit={handleSubmit} className=\"d-flex search-form position-relative\">\n      <input\n        type=\"text\"\n        placeholder=\"Tìm sản phẩm...\"\n        value={keywordText}\n        onChange={(e) => setKeywordText(e.currentTarget.value)}\n        className=\"search-input-with-icon\"\n      />\n      <button type=\"submit\" className=\"search-icon-btn\">\n        <i className=\"fas fa-search\"></i>\n      </button>\n    </form>\n  );\n}\n\nexport default SearchBox;\n"], "mappings": ";;AAAA;AACA;AACA;;AAEA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;;AAEA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;AACA;;AAEA;AACA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,QAAQ,OAAO;AAClD,SAASC,WAAW,QAAQ,kBAAkB;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE/C,SAASC,SAASA,CAAAC,IAAA,EAA0B;EAAAC,EAAA;EAAA,IAAzB;IAAEC,OAAO;IAAEC;EAAW,CAAC,GAAAH,IAAA;EACxC,MAAM,CAACI,WAAW,EAAEC,cAAc,CAAC,GAAGV,QAAQ,CAACO,OAAO,CAAC;EACvD,MAAMI,QAAQ,GAAGV,WAAW,EAAE;EAE9B,MAAMW,WAAW,GAAG,IAAIC,eAAe,CAACC,MAAM,CAACC,QAAQ,CAACC,MAAM,CAAC;EAC/D,MAAMC,UAAU,GAAGL,WAAW,CAACM,GAAG,CAAC,OAAO,CAAC,GACvCC,MAAM,CAACP,WAAW,CAACM,GAAG,CAAC,OAAO,CAAC,CAAC,GAChC,CAAC;EACL,MAAME,aAAa,GAAGR,WAAW,CAACM,GAAG,CAAC,UAAU,CAAC,GAC7CC,MAAM,CAACP,WAAW,CAACM,GAAG,CAAC,UAAU,CAAC,CAAC,GACnC,CAAC;EAEL,MAAMG,YAAY,GAAIC,CAAC,IAAK;IAC1BA,CAAC,CAACC,cAAc,EAAE;IAClBZ,QAAQ,CAAE,mBAAkBF,WAAY,UAASQ,UAAW,aAAYG,aAAc,EAAC,CAAC;IACxFZ,UAAU,CAACC,WAAW,CAAC;EACzB,CAAC;EAED,oBACEN,OAAA;IAAMqB,QAAQ,EAAEH,YAAa;IAACI,SAAS,EAAC,sCAAsC;IAAAC,QAAA,gBAC5EvB,OAAA;MACEwB,IAAI,EAAC,MAAM;MACXC,WAAW,EAAC,8BAAiB;MAC7BC,KAAK,EAAEpB,WAAY;MACnBqB,QAAQ,EAAGR,CAAC,IAAKZ,cAAc,CAACY,CAAC,CAACS,aAAa,CAACF,KAAK,CAAE;MACvDJ,SAAS,EAAC;IAAwB;MAAAO,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAClC,eACFhC,OAAA;MAAQwB,IAAI,EAAC,QAAQ;MAACF,SAAS,EAAC,iBAAiB;MAAAC,QAAA,eAC/CvB,OAAA;QAAGsB,SAAS,EAAC;MAAe;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA;IAAK;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QAC1B;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACJ;AAEX;AAAC7B,EAAA,CAhCQF,SAAS;EAAA,QAECH,WAAW;AAAA;AAAAmC,EAAA,GAFrBhC,SAAS;AAkClB,eAAeA,SAAS;AAAC,IAAAgC,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}