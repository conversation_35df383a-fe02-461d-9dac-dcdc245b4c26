{"ast": null, "code": "export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}", "map": {"version": 3, "names": ["getFreshSideObject", "top", "right", "bottom", "left"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/@popperjs/core/lib/utils/getFreshSideObject.js"], "sourcesContent": ["export default function getFreshSideObject() {\n  return {\n    top: 0,\n    right: 0,\n    bottom: 0,\n    left: 0\n  };\n}"], "mappings": "AAAA,eAAe,SAASA,kBAAkBA,CAAA,EAAG;EAC3C,OAAO;IACLC,GAAG,EAAE,CAAC;IACNC,KAAK,EAAE,CAAC;IACRC,MAAM,EAAE,CAAC;IACTC,IAAI,EAAE;EACR,CAAC;AACH"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}