{"ast": null, "code": "'use strict';\n\nmodule.exports = {\n  silentJSONParsing: true,\n  forcedJSONParsing: true,\n  clarifyTimeoutError: false\n};", "map": {"version": 3, "names": ["module", "exports", "silentJSONParsing", "forcedJSONParsing", "clarifyTimeoutError"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/axios/lib/defaults/transitional.js"], "sourcesContent": ["'use strict';\n\nmodule.exports = {\n  silentJSONParsing: true,\n  forcedJSONParsing: true,\n  clarifyTimeoutError: false\n};\n"], "mappings": "AAAA,YAAY;;AAEZA,MAAM,CAACC,OAAO,GAAG;EACfC,iBAAiB,EAAE,IAAI;EACvBC,iBAAiB,EAAE,IAAI;EACvBC,mBAAmB,EAAE;AACvB,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}