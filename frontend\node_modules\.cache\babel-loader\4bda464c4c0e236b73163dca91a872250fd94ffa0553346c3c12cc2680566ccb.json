{"ast": null, "code": "const isSVGTag = tag => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\nexport { isSVGTag };", "map": {"version": 3, "names": ["isSVGTag", "tag", "toLowerCase"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/render/svg/utils/is-svg-tag.mjs"], "sourcesContent": ["const isSVGTag = (tag) => typeof tag === \"string\" && tag.toLowerCase() === \"svg\";\n\nexport { isSVGTag };\n"], "mappings": "AAAA,MAAMA,QAAQ,GAAIC,GAAG,IAAK,OAAOA,GAAG,KAAK,QAAQ,IAAIA,GAAG,CAACC,WAAW,EAAE,KAAK,KAAK;AAEhF,SAASF,QAAQ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}