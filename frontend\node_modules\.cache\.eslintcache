[{"D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\index.js": "1", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\reportWebVitals.js": "2", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\App.js": "3", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\productsContext.js": "4", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\userContext.js": "5", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\favoriteContext.js": "6", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\payboxContext.js": "7", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\cartContext.js": "8", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\header.jsx": "9", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx": "10", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\footer.jsx": "11", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx": "12", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx": "13", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx": "14", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx": "15", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx": "16", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx": "17", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx": "18", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx": "19", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx": "20", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx": "21", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx": "22", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx": "23", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx": "24", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\UserChat.jsx": "25", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\PayboxPage.jsx": "26", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\favoritesPage.jsx": "27", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx": "28", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx": "29", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx": "30", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx": "31", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx": "32", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx": "33", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminChat.jsx": "34", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminRefund.jsx": "35", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx": "36", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminPaybox.jsx": "37", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCoupons.jsx": "38", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\services\\httpService.js": "39", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\loader.jsx": "40", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\utils\\currency.js": "41", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx": "42", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\message.jsx": "43", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\product.jsx": "44", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx": "45", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx": "46", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\rating.jsx": "47", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx": "48", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx": "49", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\chat.jsx": "50", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx": "51", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx": "52", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxWallet.jsx": "53", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDeposit.jsx": "54", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositTest.jsx": "55", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxSimpleTest.jsx": "56", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositSimple.jsx": "57", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxTransactions.jsx": "58", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDebug.jsx": "59", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\services\\couponService.js": "60", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx": "61", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositForm.jsx": "62", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx": "63", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx": "64", "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx": "65"}, {"size": 649, "mtime": 1751808079034, "results": "66", "hashOfConfig": "67"}, {"size": 362, "mtime": 1751808079045, "results": "68", "hashOfConfig": "67"}, {"size": 6051, "mtime": 1752330621618, "results": "69", "hashOfConfig": "67"}, {"size": 1890, "mtime": 1752330621621, "results": "70", "hashOfConfig": "67"}, {"size": 12219, "mtime": 1752330621622, "results": "71", "hashOfConfig": "67"}, {"size": 1715, "mtime": 1751808079033, "results": "72", "hashOfConfig": "67"}, {"size": 6337, "mtime": 1751808079033, "results": "73", "hashOfConfig": "67"}, {"size": 6833, "mtime": 1752330621620, "results": "74", "hashOfConfig": "67"}, {"size": 8603, "mtime": 1752330621620, "results": "75", "hashOfConfig": "67"}, {"size": 12783, "mtime": 1752330621626, "results": "76", "hashOfConfig": "67"}, {"size": 4284, "mtime": 1752330621619, "results": "77", "hashOfConfig": "67"}, {"size": 4693, "mtime": 1752330621627, "results": "78", "hashOfConfig": "67"}, {"size": 29081, "mtime": 1752330621629, "results": "79", "hashOfConfig": "67"}, {"size": 4681, "mtime": 1752330621624, "results": "80", "hashOfConfig": "67"}, {"size": 3271, "mtime": 1752330621632, "results": "81", "hashOfConfig": "67"}, {"size": 5028, "mtime": 1752330621631, "results": "82", "hashOfConfig": "67"}, {"size": 551, "mtime": 1751808079023, "results": "83", "hashOfConfig": "67"}, {"size": 557, "mtime": 1751808079042, "results": "84", "hashOfConfig": "67"}, {"size": 8220, "mtime": 1752330621628, "results": "85", "hashOfConfig": "67"}, {"size": 43525, "mtime": 1752330621630, "results": "86", "hashOfConfig": "67"}, {"size": 5564, "mtime": 1752330621628, "results": "87", "hashOfConfig": "67"}, {"size": 6374, "mtime": 1752330621629, "results": "88", "hashOfConfig": "67"}, {"size": 18058, "mtime": 1752330621631, "results": "89", "hashOfConfig": "67"}, {"size": 2046, "mtime": 1752330621625, "results": "90", "hashOfConfig": "67"}, {"size": 1084, "mtime": 1752330621622, "results": "91", "hashOfConfig": "67"}, {"size": 6392, "mtime": 1751808079035, "results": "92", "hashOfConfig": "67"}, {"size": 1550, "mtime": 1751808079042, "results": "93", "hashOfConfig": "67"}, {"size": 7625, "mtime": 1752330581707, "results": "94", "hashOfConfig": "67"}, {"size": 27313, "mtime": 1752330621624, "results": "95", "hashOfConfig": "67"}, {"size": 10924, "mtime": 1752330621623, "results": "96", "hashOfConfig": "67"}, {"size": 8445, "mtime": 1751808079039, "results": "97", "hashOfConfig": "67"}, {"size": 13462, "mtime": 1751808079041, "results": "98", "hashOfConfig": "67"}, {"size": 8263, "mtime": 1751808079036, "results": "99", "hashOfConfig": "67"}, {"size": 3111, "mtime": 1752330621623, "results": "100", "hashOfConfig": "67"}, {"size": 6304, "mtime": 1751808079039, "results": "101", "hashOfConfig": "67"}, {"size": 8081, "mtime": 1751808079036, "results": "102", "hashOfConfig": "67"}, {"size": 11138, "mtime": 1751808079038, "results": "103", "hashOfConfig": "67"}, {"size": 4870, "mtime": 1752330621623, "results": "104", "hashOfConfig": "67"}, {"size": 1572, "mtime": 1752330581708, "results": "105", "hashOfConfig": "67"}, {"size": 392, "mtime": 1751808079028, "results": "106", "hashOfConfig": "67"}, {"size": 1306, "mtime": 1751808079048, "results": "107", "hashOfConfig": "67"}, {"size": 2488, "mtime": 1752330621620, "results": "108", "hashOfConfig": "67"}, {"size": 228, "mtime": 1751808079028, "results": "109", "hashOfConfig": "67"}, {"size": 1499, "mtime": 1751808079030, "results": "110", "hashOfConfig": "67"}, {"size": 702, "mtime": 1751808079020, "results": "111", "hashOfConfig": "67"}, {"size": 372, "mtime": 1751808079028, "results": "112", "hashOfConfig": "67"}, {"size": 1617, "mtime": 1751808079031, "results": "113", "hashOfConfig": "67"}, {"size": 4416, "mtime": 1751808079031, "results": "114", "hashOfConfig": "67"}, {"size": 1557, "mtime": 1751808079027, "results": "115", "hashOfConfig": "67"}, {"size": 3809, "mtime": 1752330621619, "results": "116", "hashOfConfig": "67"}, {"size": 2512, "mtime": 1751808079030, "results": "117", "hashOfConfig": "67"}, {"size": 2548, "mtime": 1751808079032, "results": "118", "hashOfConfig": "67"}, {"size": 2507, "mtime": 1751808079023, "results": "119", "hashOfConfig": "67"}, {"size": 6233, "mtime": 1751808079021, "results": "120", "hashOfConfig": "67"}, {"size": 5358, "mtime": 1751808079022, "results": "121", "hashOfConfig": "67"}, {"size": 3895, "mtime": 1751808079023, "results": "122", "hashOfConfig": "67"}, {"size": 6369, "mtime": 1751808079022, "results": "123", "hashOfConfig": "67"}, {"size": 5272, "mtime": 1751808079023, "results": "124", "hashOfConfig": "67"}, {"size": 2706, "mtime": 1751808079021, "results": "125", "hashOfConfig": "67"}, {"size": 355, "mtime": 1752330621632, "results": "126", "hashOfConfig": "67"}, {"size": 445, "mtime": 1751808079025, "results": "127", "hashOfConfig": "67"}, {"size": 2966, "mtime": 1751808079022, "results": "128", "hashOfConfig": "67"}, {"size": 1826, "mtime": 1751808079030, "results": "129", "hashOfConfig": "67"}, {"size": 4570, "mtime": 1751808079026, "results": "130", "hashOfConfig": "67"}, {"size": 3449, "mtime": 1751808079024, "results": "131", "hashOfConfig": "67"}, {"filePath": "132", "messages": "133", "suppressedMessages": "134", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "1bm6rrr", {"filePath": "135", "messages": "136", "suppressedMessages": "137", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "138", "messages": "139", "suppressedMessages": "140", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "141", "messages": "142", "suppressedMessages": "143", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "144", "messages": "145", "suppressedMessages": "146", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 4, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "147", "messages": "148", "suppressedMessages": "149", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "150", "messages": "151", "suppressedMessages": "152", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "153", "messages": "154", "suppressedMessages": "155", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "156", "messages": "157", "suppressedMessages": "158", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "159", "messages": "160", "suppressedMessages": "161", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "162", "messages": "163", "suppressedMessages": "164", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 9, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "165", "messages": "166", "suppressedMessages": "167", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "168", "messages": "169", "suppressedMessages": "170", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "171", "messages": "172", "suppressedMessages": "173", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "174", "messages": "175", "suppressedMessages": "176", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "177", "messages": "178", "suppressedMessages": "179", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "180", "messages": "181", "suppressedMessages": "182", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "183", "messages": "184", "suppressedMessages": "185", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "186", "messages": "187", "suppressedMessages": "188", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "189", "messages": "190", "suppressedMessages": "191", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 7, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "192", "messages": "193", "suppressedMessages": "194", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "195", "messages": "196", "suppressedMessages": "197", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "198", "messages": "199", "suppressedMessages": "200", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "201", "messages": "202", "suppressedMessages": "203", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "204", "messages": "205", "suppressedMessages": "206", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "207", "messages": "208", "suppressedMessages": "209", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 5, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "210", "messages": "211", "suppressedMessages": "212", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "213", "messages": "214", "suppressedMessages": "215", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "216", "messages": "217", "suppressedMessages": "218", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "219", "messages": "220", "suppressedMessages": "221", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "222", "messages": "223", "suppressedMessages": "224", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "225", "messages": "226", "suppressedMessages": "227", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "228", "messages": "229", "suppressedMessages": "230", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "231", "messages": "232", "suppressedMessages": "233", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "234", "messages": "235", "suppressedMessages": "236", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "237", "messages": "238", "suppressedMessages": "239", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "240", "messages": "241", "suppressedMessages": "242", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "243", "messages": "244", "suppressedMessages": "245", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "246", "messages": "247", "suppressedMessages": "248", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 2, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "249", "messages": "250", "suppressedMessages": "251", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "252", "messages": "253", "suppressedMessages": "254", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "255", "messages": "256", "suppressedMessages": "257", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "258", "messages": "259", "suppressedMessages": "260", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "261", "messages": "262", "suppressedMessages": "263", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "264", "messages": "265", "suppressedMessages": "266", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "267", "messages": "268", "suppressedMessages": "269", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "270", "messages": "271", "suppressedMessages": "272", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "273", "messages": "274", "suppressedMessages": "275", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "276", "messages": "277", "suppressedMessages": "278", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "279", "messages": "280", "suppressedMessages": "281", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "282", "messages": "283", "suppressedMessages": "284", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "285", "messages": "286", "suppressedMessages": "287", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "288", "messages": "289", "suppressedMessages": "290", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "291", "messages": "292", "suppressedMessages": "293", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "294", "messages": "295", "suppressedMessages": "296", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "297", "messages": "298", "suppressedMessages": "299", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "300", "messages": "301", "suppressedMessages": "302", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "303", "messages": "304", "suppressedMessages": "305", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "306", "messages": "307", "suppressedMessages": "308", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 1, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "309", "messages": "310", "suppressedMessages": "311", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "312", "messages": "313", "suppressedMessages": "314", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "315", "messages": "316", "suppressedMessages": "317", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "318", "messages": "319", "suppressedMessages": "320", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 3, "fixableErrorCount": 0, "fixableWarningCount": 0, "source": null}, {"filePath": "321", "messages": "322", "suppressedMessages": "323", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, {"filePath": "324", "messages": "325", "suppressedMessages": "326", "errorCount": 0, "fatalErrorCount": 0, "warningCount": 0, "fixableErrorCount": 0, "fixableWarningCount": 0}, "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\index.js", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\reportWebVitals.js", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\App.js", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\productsContext.js", ["327"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\userContext.js", ["328", "329", "330", "331"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\favoriteContext.js", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\payboxContext.js", ["332"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\context\\cartContext.js", ["333"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\header.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\homePage.jsx", ["334", "335", "336"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\footer.jsx", ["337", "338", "339", "340", "341", "342", "343", "344", "345"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\loginPage.jsx", ["346", "347", "348"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\productPage.jsx", ["349", "350"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\cartPage.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\shippingPage.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\registerPage.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\ProtectedRoute.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\logout.jsx", ["351"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\orderDetailsPage.jsx", ["352"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\profilePage.jsx", ["353", "354", "355", "356", "357", "358", "359"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\paymentPage.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\placeOrderPage.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\searchPage.jsx", ["360"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\confirmationPage.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\UserChat.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\PayboxPage.jsx", ["361", "362", "363", "364", "365"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\favoritesPage.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminDashboard.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminProducts.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminOrders.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminReviews.jsx", ["366"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminUsers.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCategories.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminChat.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminRefund.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminBrands.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminPaybox.jsx", ["367"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\pages\\admin\\AdminCoupons.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\services\\httpService.js", ["368", "369"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\loader.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\utils\\currency.js", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\searchBox.jsx", ["370"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\message.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\product.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\AdminRedirect.jsx", ["371"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\formContainer.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\rating.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\reviewsList.jsx", ["372", "373", "374"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\checkoutSteps.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\chat.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\ordersList.jsx", ["375"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\stripePaymentWrapper.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxWallet.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDeposit.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositTest.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxSimpleTest.jsx", ["376"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositSimple.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxTransactions.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDebug.jsx", ["377"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\services\\couponService.js", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminLayout.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\PayboxDepositForm.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\paymentForm.jsx", ["378", "379", "380"], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminSidebar.jsx", [], [], "D:\\MINHNGUYET\\NAM4\\DoAn\\Python-KienTap-\\frontend\\src\\components\\admin\\AdminHeader.jsx", [], [], {"ruleId": "381", "severity": 1, "message": "382", "line": 35, "column": 49, "nodeType": "383", "messageId": "384", "endLine": 35, "endColumn": 51}, {"ruleId": "385", "severity": 1, "message": "386", "line": 81, "column": 15, "nodeType": "387", "messageId": "388", "endLine": 81, "endColumn": 19}, {"ruleId": "389", "severity": 1, "message": "390", "line": 129, "column": 6, "nodeType": "391", "endLine": 129, "endColumn": 8, "suggestions": "392"}, {"ruleId": "389", "severity": 1, "message": "393", "line": 152, "column": 6, "nodeType": "391", "endLine": 152, "endColumn": 28, "suggestions": "394"}, {"ruleId": "389", "severity": 1, "message": "395", "line": 193, "column": 6, "nodeType": "391", "endLine": 193, "endColumn": 18, "suggestions": "396"}, {"ruleId": "389", "severity": 1, "message": "397", "line": 181, "column": 6, "nodeType": "391", "endLine": 181, "endColumn": 41, "suggestions": "398"}, {"ruleId": "381", "severity": 1, "message": "382", "line": 97, "column": 27, "nodeType": "383", "messageId": "384", "endLine": 97, "endColumn": 29}, {"ruleId": "385", "severity": 1, "message": "399", "line": 1, "column": 40, "nodeType": "387", "messageId": "388", "endLine": 1, "endColumn": 48}, {"ruleId": "385", "severity": 1, "message": "400", "line": 4, "column": 8, "nodeType": "387", "messageId": "388", "endLine": 4, "endColumn": 15}, {"ruleId": "385", "severity": 1, "message": "401", "line": 38, "column": 9, "nodeType": "387", "messageId": "388", "endLine": 38, "endColumn": 27}, {"ruleId": "385", "severity": 1, "message": "402", "line": 2, "column": 10, "nodeType": "387", "messageId": "388", "endLine": 2, "endColumn": 19}, {"ruleId": "385", "severity": 1, "message": "403", "line": 2, "column": 21, "nodeType": "387", "messageId": "388", "endLine": 2, "endColumn": 24}, {"ruleId": "385", "severity": 1, "message": "404", "line": 2, "column": 26, "nodeType": "387", "messageId": "388", "endLine": 2, "endColumn": 29}, {"ruleId": "405", "severity": 1, "message": "406", "line": 14, "column": 11, "nodeType": "407", "endLine": 14, "endColumn": 50}, {"ruleId": "405", "severity": 1, "message": "406", "line": 17, "column": 11, "nodeType": "407", "endLine": 17, "endColumn": 50}, {"ruleId": "405", "severity": 1, "message": "406", "line": 20, "column": 11, "nodeType": "407", "endLine": 20, "endColumn": 50}, {"ruleId": "405", "severity": 1, "message": "406", "line": 23, "column": 11, "nodeType": "407", "endLine": 23, "endColumn": 50}, {"ruleId": "405", "severity": 1, "message": "406", "line": 26, "column": 11, "nodeType": "407", "endLine": 26, "endColumn": 50}, {"ruleId": "405", "severity": 1, "message": "406", "line": 29, "column": 11, "nodeType": "407", "endLine": 29, "endColumn": 50}, {"ruleId": "385", "severity": 1, "message": "408", "line": 3, "column": 16, "nodeType": "387", "messageId": "388", "endLine": 3, "endColumn": 22}, {"ruleId": "385", "severity": 1, "message": "403", "line": 3, "column": 24, "nodeType": "387", "messageId": "388", "endLine": 3, "endColumn": 27}, {"ruleId": "385", "severity": 1, "message": "404", "line": 3, "column": 29, "nodeType": "387", "messageId": "388", "endLine": 3, "endColumn": 32}, {"ruleId": "389", "severity": 1, "message": "409", "line": 122, "column": 6, "nodeType": "391", "endLine": 122, "endColumn": 21, "suggestions": "410"}, {"ruleId": "389", "severity": 1, "message": "411", "line": 133, "column": 6, "nodeType": "391", "endLine": 133, "endColumn": 20, "suggestions": "412"}, {"ruleId": "389", "severity": 1, "message": "413", "line": 15, "column": 6, "nodeType": "391", "endLine": 15, "endColumn": 8, "suggestions": "414"}, {"ruleId": "385", "severity": 1, "message": "415", "line": 19, "column": 10, "nodeType": "387", "messageId": "388", "endLine": 19, "endColumn": 23}, {"ruleId": "385", "severity": 1, "message": "416", "line": 9, "column": 3, "nodeType": "387", "messageId": "388", "endLine": 9, "endColumn": 7}, {"ruleId": "385", "severity": 1, "message": "417", "line": 10, "column": 3, "nodeType": "387", "messageId": "388", "endLine": 10, "endColumn": 6}, {"ruleId": "385", "severity": 1, "message": "418", "line": 13, "column": 8, "nodeType": "387", "messageId": "388", "endLine": 13, "endColumn": 15}, {"ruleId": "385", "severity": 1, "message": "419", "line": 26, "column": 14, "nodeType": "387", "messageId": "388", "endLine": 26, "endColumn": 30}, {"ruleId": "385", "severity": 1, "message": "420", "line": 57, "column": 10, "nodeType": "387", "messageId": "388", "endLine": 57, "endColumn": 16}, {"ruleId": "389", "severity": 1, "message": "421", "line": 70, "column": 6, "nodeType": "391", "endLine": 70, "endColumn": 8, "suggestions": "422"}, {"ruleId": "385", "severity": 1, "message": "423", "line": 276, "column": 9, "nodeType": "387", "messageId": "388", "endLine": 276, "endColumn": 29}, {"ruleId": "385", "severity": 1, "message": "424", "line": 23, "column": 24, "nodeType": "387", "messageId": "388", "endLine": 23, "endColumn": 39}, {"ruleId": "385", "severity": 1, "message": "425", "line": 7, "column": 8, "nodeType": "387", "messageId": "388", "endLine": 7, "endColumn": 21}, {"ruleId": "385", "severity": 1, "message": "426", "line": 9, "column": 8, "nodeType": "387", "messageId": "388", "endLine": 9, "endColumn": 25}, {"ruleId": "385", "severity": 1, "message": "427", "line": 11, "column": 8, "nodeType": "387", "messageId": "388", "endLine": 11, "endColumn": 19}, {"ruleId": "385", "severity": 1, "message": "428", "line": 12, "column": 8, "nodeType": "387", "messageId": "388", "endLine": 12, "endColumn": 24}, {"ruleId": "385", "severity": 1, "message": "429", "line": 19, "column": 11, "nodeType": "387", "messageId": "388", "endLine": 19, "endColumn": 17}, {"ruleId": "385", "severity": 1, "message": "430", "line": 9, "column": 10, "nodeType": "387", "messageId": "388", "endLine": 9, "endColumn": 18}, {"ruleId": "385", "severity": 1, "message": "431", "line": 2, "column": 65, "nodeType": "387", "messageId": "388", "endLine": 2, "endColumn": 70}, {"ruleId": "381", "severity": 1, "message": "382", "line": 13, "column": 13, "nodeType": "383", "messageId": "384", "endLine": 13, "endColumn": 15}, {"ruleId": "432", "severity": 1, "message": "433", "line": 46, "column": 1, "nodeType": "434", "endLine": 53, "endColumn": 3}, {"ruleId": "385", "severity": 1, "message": "435", "line": 40, "column": 17, "nodeType": "387", "messageId": "388", "endLine": 40, "endColumn": 26}, {"ruleId": "385", "severity": 1, "message": "436", "line": 1, "column": 8, "nodeType": "387", "messageId": "388", "endLine": 1, "endColumn": 13}, {"ruleId": "385", "severity": 1, "message": "437", "line": 2, "column": 24, "nodeType": "387", "messageId": "388", "endLine": 2, "endColumn": 33}, {"ruleId": "385", "severity": 1, "message": "403", "line": 2, "column": 35, "nodeType": "387", "messageId": "388", "endLine": 2, "endColumn": 38}, {"ruleId": "385", "severity": 1, "message": "404", "line": 2, "column": 40, "nodeType": "387", "messageId": "388", "endLine": 2, "endColumn": 43}, {"ruleId": "381", "severity": 1, "message": "382", "line": 22, "column": 47, "nodeType": "383", "messageId": "384", "endLine": 22, "endColumn": 49}, {"ruleId": "385", "severity": 1, "message": "438", "line": 4, "column": 8, "nodeType": "387", "messageId": "388", "endLine": 4, "endColumn": 19}, {"ruleId": "385", "severity": 1, "message": "435", "line": 1, "column": 29, "nodeType": "387", "messageId": "388", "endLine": 1, "endColumn": 38}, {"ruleId": "385", "severity": 1, "message": "435", "line": 1, "column": 17, "nodeType": "387", "messageId": "388", "endLine": 1, "endColumn": 26}, {"ruleId": "385", "severity": 1, "message": "438", "line": 8, "column": 8, "nodeType": "387", "messageId": "388", "endLine": 8, "endColumn": 19}, {"ruleId": "385", "severity": 1, "message": "439", "line": 14, "column": 10, "nodeType": "387", "messageId": "388", "endLine": 14, "endColumn": 15}, "eqeqeq", "Expected '===' and instead saw '=='.", "BinaryExpression", "unexpected", "no-unused-vars", "'data' is assigned a value but never used.", "Identifier", "unusedVar", "react-hooks/exhaustive-deps", "React Hook useEffect has missing dependencies: 'authTokens' and 'refresh'. Either include them or remove the dependency array.", "ArrayExpression", ["440"], "React Hook useEffect has a missing dependency: 'fetchUserProfile'. Either include it or remove the dependency array.", ["441"], "React Hook useEffect has a missing dependency: 'refresh'. Either include it or remove the dependency array.", ["442"], "React Hook useEffect has missing dependencies: 'fetchTransactions' and 'fetchWallet'. Either include them or remove the dependency array.", ["443"], "'useState' is defined but never used.", "'Product' is defined but never used.", "'discountedProducts' is assigned a value but never used.", "'Container' is defined but never used.", "'Row' is defined but never used.", "'Col' is defined but never used.", "jsx-a11y/anchor-is-valid", "The href attribute requires a valid value to be accessible. Provide a valid, navigable address as the href value. If you cannot provide a valid href, but still need the element to resemble a link, use a button and change it with appropriate styles. Learn more: https://github.com/jsx-eslint/eslint-plugin-jsx-a11y/blob/HEAD/docs/rules/anchor-is-valid.md", "JSXOpeningElement", "'Button' is defined but never used.", "React Hook useEffect has missing dependencies: 'getAvailableSizesForColor' and 'selectedSize'. Either include them or remove the dependency array.", ["444"], "React Hook useEffect has missing dependencies: 'getAvailableColorsForSize' and 'selectedColor'. Either include them or remove the dependency array.", ["445"], "React Hook useEffect has missing dependencies: 'logout', 'navigate', and 'userInfo'. Either include them or remove the dependency array.", ["446"], "'payboxMessage' is assigned a value but never used.", "'Tabs' is defined but never used.", "'Tab' is defined but never used.", "'Message' is defined but never used.", "'favoritesLoading' is assigned a value but never used.", "'avatar' is assigned a value but never used.", "React Hook useEffect has missing dependencies: 'navigate' and 'userInfo'. Either include them or remove the dependency array.", ["447"], "'handlePasswordChange' is assigned a value but never used.", "'setSearchParams' is assigned a value but never used.", "'PayboxDeposit' is defined but never used.", "'PayboxDepositTest' is defined but never used.", "'PayboxDebug' is defined but never used.", "'PayboxSimpleTest' is defined but never used.", "'wallet' is assigned a value but never used.", "'products' is assigned a value but never used.", "'Alert' is defined but never used.", "import/no-anonymous-default-export", "Assign object to a variable before exporting as module default", "ExportDefaultDeclaration", "'useEffect' is defined but never used.", "'React' is defined but never used.", "'ListGroup' is defined but never used.", "'httpService' is defined but never used.", "'email' is assigned a value but never used.", {"desc": "448", "fix": "449"}, {"desc": "450", "fix": "451"}, {"desc": "448", "fix": "452"}, {"desc": "453", "fix": "454"}, {"desc": "455", "fix": "456"}, {"desc": "457", "fix": "458"}, {"desc": "459", "fix": "460"}, {"desc": "461", "fix": "462"}, "Update the dependencies array to be: [authTokens, refresh]", {"range": "463", "text": "464"}, "Update the dependencies array to be: [authTokens, fetchUserProfile, userInfo]", {"range": "465", "text": "466"}, {"range": "467", "text": "464"}, "Update the dependencies array to be: [userInfo, authTokens, userLoading, fetchWallet, fetchTransactions]", {"range": "468", "text": "469"}, "Update the dependencies array to be: [getAvailableSizesForColor, selectedColor, selectedSize]", {"range": "470", "text": "471"}, "Update the dependencies array to be: [getAvailableColorsForSize, selectedColor, selectedSize]", {"range": "472", "text": "473"}, "Update the dependencies array to be: [logout, navigate, userInfo]", {"range": "474", "text": "475"}, "Update the dependencies array to be: [navigate, userInfo]", {"range": "476", "text": "477"}, [4199, 4201], "[authTokens, refresh]", [4943, 4965], "[authTokens, fetchUserProfile, userInfo]", [6413, 6425], [5731, 5766], "[userInfo, authTokens, userLoading, fetchWallet, fetchTransactions]", [4149, 4164], "[getAvailableSizesForColor, selectedColor, selectedSize]", [4608, 4622], "[getAvailableColorsForSize, selectedColor, selectedSize]", [491, 493], "[logout, navigate, userInfo]", [2546, 2548], "[navigate, userInfo]"]