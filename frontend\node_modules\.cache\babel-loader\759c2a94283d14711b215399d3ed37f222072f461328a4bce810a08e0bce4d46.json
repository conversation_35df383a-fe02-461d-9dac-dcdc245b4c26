{"ast": null, "code": "import { isBezierDefinition } from '../../../easing/utils/is-bezier-definition.mjs';\nfunction isWaapiSupportedEasing(easing) {\n  return Boolean(!easing || typeof easing === \"string\" && supportedWaapiEasing[easing] || isBezierDefinition(easing) || Array.isArray(easing) && easing.every(isWaapiSupportedEasing));\n}\nconst cubicBezierAsString = _ref => {\n  let [a, b, c, d] = _ref;\n  return `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\n};\nconst supportedWaapiEasing = {\n  linear: \"linear\",\n  ease: \"ease\",\n  easeIn: \"ease-in\",\n  easeOut: \"ease-out\",\n  easeInOut: \"ease-in-out\",\n  circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n  circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n  backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n  backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99])\n};\nfunction mapEasingToNativeEasing(easing) {\n  if (!easing) return undefined;\n  return isBezierDefinition(easing) ? cubicBezierAsString(easing) : Array.isArray(easing) ? easing.map(mapEasingToNativeEasing) : supportedWaapiEasing[easing];\n}\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };", "map": {"version": 3, "names": ["isBezierDefinition", "isWaapiSupportedEasing", "easing", "Boolean", "supportedWaapiEasing", "Array", "isArray", "every", "cubicBezierAsString", "_ref", "a", "b", "c", "d", "linear", "ease", "easeIn", "easeOut", "easeInOut", "circIn", "circOut", "backIn", "backOut", "mapEasingToNativeEasing", "undefined", "map"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/animation/animators/waapi/easing.mjs"], "sourcesContent": ["import { isBezierDefinition } from '../../../easing/utils/is-bezier-definition.mjs';\n\nfunction isWaapiSupportedEasing(easing) {\n    return Boolean(!easing ||\n        (typeof easing === \"string\" && supportedWaapiEasing[easing]) ||\n        isBezierDefinition(easing) ||\n        (Array.isArray(easing) && easing.every(isWaapiSupportedEasing)));\n}\nconst cubicBezierAsString = ([a, b, c, d]) => `cubic-bezier(${a}, ${b}, ${c}, ${d})`;\nconst supportedWaapiEasing = {\n    linear: \"linear\",\n    ease: \"ease\",\n    easeIn: \"ease-in\",\n    easeOut: \"ease-out\",\n    easeInOut: \"ease-in-out\",\n    circIn: cubicBezierAsString([0, 0.65, 0.55, 1]),\n    circOut: cubicBezierAsString([0.55, 0, 1, 0.45]),\n    backIn: cubicBezierAsString([0.31, 0.01, 0.66, -0.59]),\n    backOut: cubicBezierAsString([0.33, 1.53, 0.69, 0.99]),\n};\nfunction mapEasingToNativeEasing(easing) {\n    if (!easing)\n        return undefined;\n    return isBezierDefinition(easing)\n        ? cubicBezierAsString(easing)\n        : Array.isArray(easing)\n            ? easing.map(mapEasingToNativeEasing)\n            : supportedWaapiEasing[easing];\n}\n\nexport { cubicBezierAsString, isWaapiSupportedEasing, mapEasingToNativeEasing, supportedWaapiEasing };\n"], "mappings": "AAAA,SAASA,kBAAkB,QAAQ,gDAAgD;AAEnF,SAASC,sBAAsBA,CAACC,MAAM,EAAE;EACpC,OAAOC,OAAO,CAAC,CAACD,MAAM,IACjB,OAAOA,MAAM,KAAK,QAAQ,IAAIE,oBAAoB,CAACF,MAAM,CAAE,IAC5DF,kBAAkB,CAACE,MAAM,CAAC,IACzBG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,IAAIA,MAAM,CAACK,KAAK,CAACN,sBAAsB,CAAE,CAAC;AACxE;AACA,MAAMO,mBAAmB,GAAGC,IAAA;EAAA,IAAC,CAACC,CAAC,EAAEC,CAAC,EAAEC,CAAC,EAAEC,CAAC,CAAC,GAAAJ,IAAA;EAAA,OAAM,gBAAeC,CAAE,KAAIC,CAAE,KAAIC,CAAE,KAAIC,CAAE,GAAE;AAAA;AACpF,MAAMT,oBAAoB,GAAG;EACzBU,MAAM,EAAE,QAAQ;EAChBC,IAAI,EAAE,MAAM;EACZC,MAAM,EAAE,SAAS;EACjBC,OAAO,EAAE,UAAU;EACnBC,SAAS,EAAE,aAAa;EACxBC,MAAM,EAAEX,mBAAmB,CAAC,CAAC,CAAC,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,CAAC,CAAC;EAC/CY,OAAO,EAAEZ,mBAAmB,CAAC,CAAC,IAAI,EAAE,CAAC,EAAE,CAAC,EAAE,IAAI,CAAC,CAAC;EAChDa,MAAM,EAAEb,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,CAAC,IAAI,CAAC,CAAC;EACtDc,OAAO,EAAEd,mBAAmB,CAAC,CAAC,IAAI,EAAE,IAAI,EAAE,IAAI,EAAE,IAAI,CAAC;AACzD,CAAC;AACD,SAASe,uBAAuBA,CAACrB,MAAM,EAAE;EACrC,IAAI,CAACA,MAAM,EACP,OAAOsB,SAAS;EACpB,OAAOxB,kBAAkB,CAACE,MAAM,CAAC,GAC3BM,mBAAmB,CAACN,MAAM,CAAC,GAC3BG,KAAK,CAACC,OAAO,CAACJ,MAAM,CAAC,GACjBA,MAAM,CAACuB,GAAG,CAACF,uBAAuB,CAAC,GACnCnB,oBAAoB,CAACF,MAAM,CAAC;AAC1C;AAEA,SAASM,mBAAmB,EAAEP,sBAAsB,EAAEsB,uBAAuB,EAAEnB,oBAAoB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}