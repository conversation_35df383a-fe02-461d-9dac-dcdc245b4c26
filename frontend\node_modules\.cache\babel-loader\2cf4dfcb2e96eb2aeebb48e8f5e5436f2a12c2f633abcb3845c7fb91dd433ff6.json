{"ast": null, "code": "var _jsxFileName = \"D:\\\\MINHNGUYET\\\\NAM4\\\\DoAn\\\\Python-KienTap-\\\\frontend\\\\src\\\\components\\\\chat.jsx\",\n  _s = $RefreshSig$();\nimport React, { useEffect, useState, useRef } from \"react\";\nimport { jsxDEV as _jsxDEV } from \"react/jsx-dev-runtime\";\nfunction ChatBox(_ref) {\n  _s();\n  let {\n    userId,\n    currentUserId,\n    token,\n    userName,\n    isAdmin\n  } = _ref;\n  const [messages, setMessages] = useState([]);\n  const [input, setInput] = useState(\"\");\n  const ws = useRef(null);\n  const bottomRef = useRef(null);\n  const roomName = [userId, currentUserId].sort().join(\"_\");\n  useEffect(() => {\n    fetch(`/api/chat/messages/${roomName}/`, {\n      headers: {\n        Authorization: `JWT ${token}`\n      }\n    }).then(res => res.json()).then(setMessages);\n    ws.current = new WebSocket(`ws://localhost:8000/ws/chat/${roomName}/`);\n    ws.current.onmessage = e => {\n      const data = JSON.parse(e.data);\n      setMessages(prev => [...prev, data]);\n    };\n    return () => ws.current.close();\n  }, [roomName, token]);\n  useEffect(() => {\n    if (bottomRef.current) {\n      bottomRef.current.scrollIntoView({\n        behavior: \"smooth\",\n        block: \"nearest\" // 💥 CHÌA KHOÁ ở đây!\n      });\n    }\n  }, [messages]);\n  const sendMessage = () => {\n    if (!input.trim()) return;\n    ws.current.send(JSON.stringify({\n      message: input,\n      sender_id: currentUserId\n    }));\n    setInput(\"\");\n  };\n  return /*#__PURE__*/_jsxDEV(\"div\", {\n    style: {\n      maxWidth: \"auto\",\n      margin: \"0 auto\"\n    },\n    children: [/*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        height: 350,\n        overflowY: \"auto\",\n        border: \"1px solid #ddd\",\n        borderRadius: 8,\n        padding: 12,\n        backgroundColor: \"#f9f9f9\",\n        marginBottom: 12\n      },\n      children: [messages.map((msg, idx) => /*#__PURE__*/_jsxDEV(\"div\", {\n        style: {\n          display: \"flex\",\n          flexDirection: \"column\",\n          alignItems: msg.sender_id === currentUserId ? \"flex-end\" : \"flex-start\",\n          marginBottom: 10\n        },\n        children: [/*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            fontSize: \"0.85rem\",\n            fontWeight: \"bold\",\n            marginBottom: 4,\n            color: \"#555\"\n          },\n          children: msg.sender_id === currentUserId ? isAdmin ? \"Admin\" : userName || \"User\" : isAdmin ? userName || \"User\" : \"Admin\"\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 69,\n          columnNumber: 13\n        }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n          style: {\n            backgroundColor: msg.sender_id === currentUserId ? \"#4CAF50\" : \"#e0e0e0\",\n            color: msg.sender_id === currentUserId ? \"#fff\" : \"#000\",\n            padding: \"8px 12px\",\n            borderRadius: 16,\n            maxWidth: \"75%\",\n            wordBreak: \"break-word\"\n          },\n          children: msg.message\n        }, void 0, false, {\n          fileName: _jsxFileName,\n          lineNumber: 87,\n          columnNumber: 13\n        }, this)]\n      }, idx, true, {\n        fileName: _jsxFileName,\n        lineNumber: 58,\n        columnNumber: 11\n      }, this)), /*#__PURE__*/_jsxDEV(\"div\", {\n        ref: bottomRef\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 103,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 46,\n      columnNumber: 7\n    }, this), /*#__PURE__*/_jsxDEV(\"div\", {\n      style: {\n        display: \"flex\",\n        gap: 8\n      },\n      children: [/*#__PURE__*/_jsxDEV(\"input\", {\n        value: input,\n        onChange: e => setInput(e.target.value),\n        onKeyDown: e => e.key === \"Enter\" && sendMessage(),\n        style: {\n          flex: 1,\n          padding: \"10px 14px\",\n          borderRadius: 20,\n          border: \"1px solid #ccc\",\n          outline: \"none\",\n          fontSize: 14\n        },\n        placeholder: \"Nh\\u1EADp tin nh\\u1EAFn...\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 108,\n        columnNumber: 9\n      }, this), /*#__PURE__*/_jsxDEV(\"button\", {\n        onClick: sendMessage,\n        style: {\n          backgroundColor: \"#4CAF50\",\n          color: \"white\",\n          padding: \"10px 16px\",\n          border: \"none\",\n          borderRadius: 20,\n          cursor: \"pointer\",\n          fontWeight: \"bold\"\n        },\n        children: \"G\\u1EEDi\"\n      }, void 0, false, {\n        fileName: _jsxFileName,\n        lineNumber: 122,\n        columnNumber: 9\n      }, this)]\n    }, void 0, true, {\n      fileName: _jsxFileName,\n      lineNumber: 107,\n      columnNumber: 7\n    }, this)]\n  }, void 0, true, {\n    fileName: _jsxFileName,\n    lineNumber: 45,\n    columnNumber: 5\n  }, this);\n}\n_s(ChatBox, \"q/Ink+tgjqzo+0Hkw44hWDYhqTQ=\");\n_c = ChatBox;\nexport default ChatBox;\nvar _c;\n$RefreshReg$(_c, \"ChatBox\");", "map": {"version": 3, "names": ["React", "useEffect", "useState", "useRef", "jsxDEV", "_jsxDEV", "ChatBox", "_ref", "_s", "userId", "currentUserId", "token", "userName", "isAdmin", "messages", "setMessages", "input", "setInput", "ws", "bottomRef", "roomName", "sort", "join", "fetch", "headers", "Authorization", "then", "res", "json", "current", "WebSocket", "onmessage", "e", "data", "JSON", "parse", "prev", "close", "scrollIntoView", "behavior", "block", "sendMessage", "trim", "send", "stringify", "message", "sender_id", "style", "max<PERSON><PERSON><PERSON>", "margin", "children", "height", "overflowY", "border", "borderRadius", "padding", "backgroundColor", "marginBottom", "map", "msg", "idx", "display", "flexDirection", "alignItems", "fontSize", "fontWeight", "color", "fileName", "_jsxFileName", "lineNumber", "columnNumber", "wordBreak", "ref", "gap", "value", "onChange", "target", "onKeyDown", "key", "flex", "outline", "placeholder", "onClick", "cursor", "_c", "$RefreshReg$"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/src/components/chat.jsx"], "sourcesContent": ["import React, { useEffect, useState, useRef } from \"react\";\n\nfunction ChatBox({ userId, currentUserId, token, userName, isAdmin }) {\n  const [messages, setMessages] = useState([]);\n  const [input, setInput] = useState(\"\");\n  const ws = useRef(null);\n  const bottomRef = useRef(null);\n\n  const roomName = [userId, currentUserId].sort().join(\"_\");\n\n  useEffect(() => {\n    fetch(`/api/chat/messages/${roomName}/`, {\n      headers: { Authorization: `JWT ${token}` },\n    })\n      .then((res) => res.json())\n      .then(setMessages);\n\n    ws.current = new WebSocket(`ws://localhost:8000/ws/chat/${roomName}/`);\n    ws.current.onmessage = (e) => {\n      const data = JSON.parse(e.data);\n      setMessages((prev) => [...prev, data]);\n    };\n\n    return () => ws.current.close();\n  }, [roomName, token]);\n\n  useEffect(() => {\n    if (bottomRef.current) {\n      bottomRef.current.scrollIntoView({\n        behavior: \"smooth\",\n        block: \"nearest\", // 💥 CHÌA KHOÁ ở đây!\n      });\n    }\n  }, [messages]);\n\n  const sendMessage = () => {\n    if (!input.trim()) return;\n    ws.current.send(\n      JSON.stringify({ message: input, sender_id: currentUserId })\n    );\n    setInput(\"\");\n  };\n\n  return (\n    <div style={{ maxWidth: \"auto\", margin: \"0 auto\" }}>\n      <div\n        style={{\n          height: 350,\n          overflowY: \"auto\",\n          border: \"1px solid #ddd\",\n          borderRadius: 8,\n          padding: 12,\n          backgroundColor: \"#f9f9f9\",\n          marginBottom: 12,\n        }}\n      >\n        {messages.map((msg, idx) => (\n          <div\n            key={idx}\n            style={{\n              display: \"flex\",\n              flexDirection: \"column\",\n              alignItems:\n                msg.sender_id === currentUserId ? \"flex-end\" : \"flex-start\",\n              marginBottom: 10,\n            }}\n          >\n            {/* Tên người gửi */}\n            <div\n              style={{\n                fontSize: \"0.85rem\",\n                fontWeight: \"bold\",\n                marginBottom: 4,\n                color: \"#555\",\n              }}\n            >\n              {msg.sender_id === currentUserId\n                ? isAdmin\n                  ? \"Admin\"\n                  : userName || \"User\"\n                : isAdmin\n                ? userName || \"User\"\n                : \"Admin\"}\n            </div>\n\n            {/* Nội dung tin nhắn */}\n            <div\n              style={{\n                backgroundColor:\n                  msg.sender_id === currentUserId ? \"#4CAF50\" : \"#e0e0e0\",\n                color: msg.sender_id === currentUserId ? \"#fff\" : \"#000\",\n                padding: \"8px 12px\",\n                borderRadius: 16,\n                maxWidth: \"75%\",\n                wordBreak: \"break-word\",\n              }}\n            >\n              {msg.message}\n            </div>\n          </div>\n        ))}\n\n        <div ref={bottomRef} />\n      </div>\n\n      {/* Ô nhập và nút gửi */}\n      <div style={{ display: \"flex\", gap: 8 }}>\n        <input\n          value={input}\n          onChange={(e) => setInput(e.target.value)}\n          onKeyDown={(e) => e.key === \"Enter\" && sendMessage()}\n          style={{\n            flex: 1,\n            padding: \"10px 14px\",\n            borderRadius: 20,\n            border: \"1px solid #ccc\",\n            outline: \"none\",\n            fontSize: 14,\n          }}\n          placeholder=\"Nhập tin nhắn...\"\n        />\n        <button\n          onClick={sendMessage}\n          style={{\n            backgroundColor: \"#4CAF50\",\n            color: \"white\",\n            padding: \"10px 16px\",\n            border: \"none\",\n            borderRadius: 20,\n            cursor: \"pointer\",\n            fontWeight: \"bold\",\n          }}\n        >\n          Gửi\n        </button>\n      </div>\n    </div>\n  );\n}\n\nexport default ChatBox;\n"], "mappings": ";;AAAA,OAAOA,KAAK,IAAIC,SAAS,EAAEC,QAAQ,EAAEC,MAAM,QAAQ,OAAO;AAAC,SAAAC,MAAA,IAAAC,OAAA;AAE3D,SAASC,OAAOA,CAAAC,IAAA,EAAsD;EAAAC,EAAA;EAAA,IAArD;IAAEC,MAAM;IAAEC,aAAa;IAAEC,KAAK;IAAEC,QAAQ;IAAEC;EAAQ,CAAC,GAAAN,IAAA;EAClE,MAAM,CAACO,QAAQ,EAAEC,WAAW,CAAC,GAAGb,QAAQ,CAAC,EAAE,CAAC;EAC5C,MAAM,CAACc,KAAK,EAAEC,QAAQ,CAAC,GAAGf,QAAQ,CAAC,EAAE,CAAC;EACtC,MAAMgB,EAAE,GAAGf,MAAM,CAAC,IAAI,CAAC;EACvB,MAAMgB,SAAS,GAAGhB,MAAM,CAAC,IAAI,CAAC;EAE9B,MAAMiB,QAAQ,GAAG,CAACX,MAAM,EAAEC,aAAa,CAAC,CAACW,IAAI,EAAE,CAACC,IAAI,CAAC,GAAG,CAAC;EAEzDrB,SAAS,CAAC,MAAM;IACdsB,KAAK,CAAE,sBAAqBH,QAAS,GAAE,EAAE;MACvCI,OAAO,EAAE;QAAEC,aAAa,EAAG,OAAMd,KAAM;MAAE;IAC3C,CAAC,CAAC,CACCe,IAAI,CAAEC,GAAG,IAAKA,GAAG,CAACC,IAAI,EAAE,CAAC,CACzBF,IAAI,CAACX,WAAW,CAAC;IAEpBG,EAAE,CAACW,OAAO,GAAG,IAAIC,SAAS,CAAE,+BAA8BV,QAAS,GAAE,CAAC;IACtEF,EAAE,CAACW,OAAO,CAACE,SAAS,GAAIC,CAAC,IAAK;MAC5B,MAAMC,IAAI,GAAGC,IAAI,CAACC,KAAK,CAACH,CAAC,CAACC,IAAI,CAAC;MAC/BlB,WAAW,CAAEqB,IAAI,IAAK,CAAC,GAAGA,IAAI,EAAEH,IAAI,CAAC,CAAC;IACxC,CAAC;IAED,OAAO,MAAMf,EAAE,CAACW,OAAO,CAACQ,KAAK,EAAE;EACjC,CAAC,EAAE,CAACjB,QAAQ,EAAET,KAAK,CAAC,CAAC;EAErBV,SAAS,CAAC,MAAM;IACd,IAAIkB,SAAS,CAACU,OAAO,EAAE;MACrBV,SAAS,CAACU,OAAO,CAACS,cAAc,CAAC;QAC/BC,QAAQ,EAAE,QAAQ;QAClBC,KAAK,EAAE,SAAS,CAAE;MACpB,CAAC,CAAC;IACJ;EACF,CAAC,EAAE,CAAC1B,QAAQ,CAAC,CAAC;EAEd,MAAM2B,WAAW,GAAGA,CAAA,KAAM;IACxB,IAAI,CAACzB,KAAK,CAAC0B,IAAI,EAAE,EAAE;IACnBxB,EAAE,CAACW,OAAO,CAACc,IAAI,CACbT,IAAI,CAACU,SAAS,CAAC;MAAEC,OAAO,EAAE7B,KAAK;MAAE8B,SAAS,EAAEpC;IAAc,CAAC,CAAC,CAC7D;IACDO,QAAQ,CAAC,EAAE,CAAC;EACd,CAAC;EAED,oBACEZ,OAAA;IAAK0C,KAAK,EAAE;MAAEC,QAAQ,EAAE,MAAM;MAAEC,MAAM,EAAE;IAAS,CAAE;IAAAC,QAAA,gBACjD7C,OAAA;MACE0C,KAAK,EAAE;QACLI,MAAM,EAAE,GAAG;QACXC,SAAS,EAAE,MAAM;QACjBC,MAAM,EAAE,gBAAgB;QACxBC,YAAY,EAAE,CAAC;QACfC,OAAO,EAAE,EAAE;QACXC,eAAe,EAAE,SAAS;QAC1BC,YAAY,EAAE;MAChB,CAAE;MAAAP,QAAA,GAEDpC,QAAQ,CAAC4C,GAAG,CAAC,CAACC,GAAG,EAAEC,GAAG,kBACrBvD,OAAA;QAEE0C,KAAK,EAAE;UACLc,OAAO,EAAE,MAAM;UACfC,aAAa,EAAE,QAAQ;UACvBC,UAAU,EACRJ,GAAG,CAACb,SAAS,KAAKpC,aAAa,GAAG,UAAU,GAAG,YAAY;UAC7D+C,YAAY,EAAE;QAChB,CAAE;QAAAP,QAAA,gBAGF7C,OAAA;UACE0C,KAAK,EAAE;YACLiB,QAAQ,EAAE,SAAS;YACnBC,UAAU,EAAE,MAAM;YAClBR,YAAY,EAAE,CAAC;YACfS,KAAK,EAAE;UACT,CAAE;UAAAhB,QAAA,EAEDS,GAAG,CAACb,SAAS,KAAKpC,aAAa,GAC5BG,OAAO,GACL,OAAO,GACPD,QAAQ,IAAI,MAAM,GACpBC,OAAO,GACPD,QAAQ,IAAI,MAAM,GAClB;QAAO;UAAAuD,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACP,eAGNjE,OAAA;UACE0C,KAAK,EAAE;YACLS,eAAe,EACbG,GAAG,CAACb,SAAS,KAAKpC,aAAa,GAAG,SAAS,GAAG,SAAS;YACzDwD,KAAK,EAAEP,GAAG,CAACb,SAAS,KAAKpC,aAAa,GAAG,MAAM,GAAG,MAAM;YACxD6C,OAAO,EAAE,UAAU;YACnBD,YAAY,EAAE,EAAE;YAChBN,QAAQ,EAAE,KAAK;YACfuB,SAAS,EAAE;UACb,CAAE;UAAArB,QAAA,EAEDS,GAAG,CAACd;QAAO;UAAAsB,QAAA,EAAAC,YAAA;UAAAC,UAAA;UAAAC,YAAA;QAAA,QACR;MAAA,GAxCDV,GAAG;QAAAO,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QA0CX,CAAC,eAEFjE,OAAA;QAAKmE,GAAG,EAAErD;MAAU;QAAAgD,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAG;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACnB,eAGNjE,OAAA;MAAK0C,KAAK,EAAE;QAAEc,OAAO,EAAE,MAAM;QAAEY,GAAG,EAAE;MAAE,CAAE;MAAAvB,QAAA,gBACtC7C,OAAA;QACEqE,KAAK,EAAE1D,KAAM;QACb2D,QAAQ,EAAG3C,CAAC,IAAKf,QAAQ,CAACe,CAAC,CAAC4C,MAAM,CAACF,KAAK,CAAE;QAC1CG,SAAS,EAAG7C,CAAC,IAAKA,CAAC,CAAC8C,GAAG,KAAK,OAAO,IAAIrC,WAAW,EAAG;QACrDM,KAAK,EAAE;UACLgC,IAAI,EAAE,CAAC;UACPxB,OAAO,EAAE,WAAW;UACpBD,YAAY,EAAE,EAAE;UAChBD,MAAM,EAAE,gBAAgB;UACxB2B,OAAO,EAAE,MAAM;UACfhB,QAAQ,EAAE;QACZ,CAAE;QACFiB,WAAW,EAAC;MAAkB;QAAAd,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAC9B,eACFjE,OAAA;QACE6E,OAAO,EAAEzC,WAAY;QACrBM,KAAK,EAAE;UACLS,eAAe,EAAE,SAAS;UAC1BU,KAAK,EAAE,OAAO;UACdX,OAAO,EAAE,WAAW;UACpBF,MAAM,EAAE,MAAM;UACdC,YAAY,EAAE,EAAE;UAChB6B,MAAM,EAAE,SAAS;UACjBlB,UAAU,EAAE;QACd,CAAE;QAAAf,QAAA,EACH;MAED;QAAAiB,QAAA,EAAAC,YAAA;QAAAC,UAAA;QAAAC,YAAA;MAAA,QAAS;IAAA;MAAAH,QAAA,EAAAC,YAAA;MAAAC,UAAA;MAAAC,YAAA;IAAA,QACL;EAAA;IAAAH,QAAA,EAAAC,YAAA;IAAAC,UAAA;IAAAC,YAAA;EAAA,QACF;AAEV;AAAC9D,EAAA,CAxIQF,OAAO;AAAA8E,EAAA,GAAP9E,OAAO;AA0IhB,eAAeA,OAAO;AAAC,IAAA8E,EAAA;AAAAC,YAAA,CAAAD,EAAA"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}