{"ast": null, "code": "import { useMemo } from 'react';\nimport { isForcedMotionValue } from '../../motion/utils/is-forced-motion-value.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\nfunction copyRawValuesOnly(target, source, props) {\n  for (const key in source) {\n    if (!isMotionValue(source[key]) && !isForcedMotionValue(key, props)) {\n      target[key] = source[key];\n    }\n  }\n}\nfunction useInitialMotionValues(_ref, visualState, isStatic) {\n  let {\n    transformTemplate\n  } = _ref;\n  return useMemo(() => {\n    const state = createHtmlRenderState();\n    buildHTMLStyles(state, visualState, {\n      enableHardwareAcceleration: !isStatic\n    }, transformTemplate);\n    return Object.assign({}, state.vars, state.style);\n  }, [visualState]);\n}\nfunction useStyle(props, visualState, isStatic) {\n  const styleProp = props.style || {};\n  const style = {};\n  /**\n   * Copy non-Motion Values straight into style\n   */\n  copyRawValuesOnly(style, styleProp, props);\n  Object.assign(style, useInitialMotionValues(props, visualState, isStatic));\n  return props.transformValues ? props.transformValues(style) : style;\n}\nfunction useHTMLProps(props, visualState, isStatic) {\n  // The `any` isn't ideal but it is the type of createElement props argument\n  const htmlProps = {};\n  const style = useStyle(props, visualState, isStatic);\n  if (props.drag && props.dragListener !== false) {\n    // Disable the ghost element when a user drags\n    htmlProps.draggable = false;\n    // Disable text selection\n    style.userSelect = style.WebkitUserSelect = style.WebkitTouchCallout = \"none\";\n    // Disable scrolling on the draggable direction\n    style.touchAction = props.drag === true ? \"none\" : `pan-${props.drag === \"x\" ? \"y\" : \"x\"}`;\n  }\n  if (props.tabIndex === undefined && (props.onTap || props.onTapStart || props.whileTap)) {\n    htmlProps.tabIndex = 0;\n  }\n  htmlProps.style = style;\n  return htmlProps;\n}\nexport { copyRawValuesOnly, useHTMLProps };", "map": {"version": 3, "names": ["useMemo", "isForcedMotionValue", "isMotionValue", "buildHTMLStyles", "createHtmlRenderState", "copyRawValuesOnly", "target", "source", "props", "key", "useInitialMotionValues", "_ref", "visualState", "isStatic", "transformTemplate", "state", "enableHardwareAcceleration", "Object", "assign", "vars", "style", "useStyle", "styleProp", "transformValues", "useHTMLProps", "htmlProps", "drag", "dragListener", "draggable", "userSelect", "WebkitUserSelect", "WebkitTouchCallout", "touchAction", "tabIndex", "undefined", "onTap", "onTapStart", "whileTap"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/render/html/use-props.mjs"], "sourcesContent": ["import { useMemo } from 'react';\nimport { isForcedMotionValue } from '../../motion/utils/is-forced-motion-value.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { buildHTMLStyles } from './utils/build-styles.mjs';\nimport { createHtmlRenderState } from './utils/create-render-state.mjs';\n\nfunction copyRawValuesOnly(target, source, props) {\n    for (const key in source) {\n        if (!isMotionValue(source[key]) && !isForcedMotionValue(key, props)) {\n            target[key] = source[key];\n        }\n    }\n}\nfunction useInitialMotionValues({ transformTemplate }, visualState, isStatic) {\n    return useMemo(() => {\n        const state = createHtmlRenderState();\n        buildHTMLStyles(state, visualState, { enableHardwareAcceleration: !isStatic }, transformTemplate);\n        return Object.assign({}, state.vars, state.style);\n    }, [visualState]);\n}\nfunction useStyle(props, visualState, isStatic) {\n    const styleProp = props.style || {};\n    const style = {};\n    /**\n     * Copy non-Motion Values straight into style\n     */\n    copyRawValuesOnly(style, styleProp, props);\n    Object.assign(style, useInitialMotionValues(props, visualState, isStatic));\n    return props.transformValues ? props.transformValues(style) : style;\n}\nfunction useHTMLProps(props, visualState, isStatic) {\n    // The `any` isn't ideal but it is the type of createElement props argument\n    const htmlProps = {};\n    const style = useStyle(props, visualState, isStatic);\n    if (props.drag && props.dragListener !== false) {\n        // Disable the ghost element when a user drags\n        htmlProps.draggable = false;\n        // Disable text selection\n        style.userSelect =\n            style.WebkitUserSelect =\n                style.WebkitTouchCallout =\n                    \"none\";\n        // Disable scrolling on the draggable direction\n        style.touchAction =\n            props.drag === true\n                ? \"none\"\n                : `pan-${props.drag === \"x\" ? \"y\" : \"x\"}`;\n    }\n    if (props.tabIndex === undefined &&\n        (props.onTap || props.onTapStart || props.whileTap)) {\n        htmlProps.tabIndex = 0;\n    }\n    htmlProps.style = style;\n    return htmlProps;\n}\n\nexport { copyRawValuesOnly, useHTMLProps };\n"], "mappings": "AAAA,SAASA,OAAO,QAAQ,OAAO;AAC/B,SAASC,mBAAmB,QAAQ,+CAA+C;AACnF,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,eAAe,QAAQ,0BAA0B;AAC1D,SAASC,qBAAqB,QAAQ,iCAAiC;AAEvE,SAASC,iBAAiBA,CAACC,MAAM,EAAEC,MAAM,EAAEC,KAAK,EAAE;EAC9C,KAAK,MAAMC,GAAG,IAAIF,MAAM,EAAE;IACtB,IAAI,CAACL,aAAa,CAACK,MAAM,CAACE,GAAG,CAAC,CAAC,IAAI,CAACR,mBAAmB,CAACQ,GAAG,EAAED,KAAK,CAAC,EAAE;MACjEF,MAAM,CAACG,GAAG,CAAC,GAAGF,MAAM,CAACE,GAAG,CAAC;IAC7B;EACJ;AACJ;AACA,SAASC,sBAAsBA,CAAAC,IAAA,EAAwBC,WAAW,EAAEC,QAAQ,EAAE;EAAA,IAA9C;IAAEC;EAAkB,CAAC,GAAAH,IAAA;EACjD,OAAOX,OAAO,CAAC,MAAM;IACjB,MAAMe,KAAK,GAAGX,qBAAqB,EAAE;IACrCD,eAAe,CAACY,KAAK,EAAEH,WAAW,EAAE;MAAEI,0BAA0B,EAAE,CAACH;IAAS,CAAC,EAAEC,iBAAiB,CAAC;IACjG,OAAOG,MAAM,CAACC,MAAM,CAAC,CAAC,CAAC,EAAEH,KAAK,CAACI,IAAI,EAAEJ,KAAK,CAACK,KAAK,CAAC;EACrD,CAAC,EAAE,CAACR,WAAW,CAAC,CAAC;AACrB;AACA,SAASS,QAAQA,CAACb,KAAK,EAAEI,WAAW,EAAEC,QAAQ,EAAE;EAC5C,MAAMS,SAAS,GAAGd,KAAK,CAACY,KAAK,IAAI,CAAC,CAAC;EACnC,MAAMA,KAAK,GAAG,CAAC,CAAC;EAChB;AACJ;AACA;EACIf,iBAAiB,CAACe,KAAK,EAAEE,SAAS,EAAEd,KAAK,CAAC;EAC1CS,MAAM,CAACC,MAAM,CAACE,KAAK,EAAEV,sBAAsB,CAACF,KAAK,EAAEI,WAAW,EAAEC,QAAQ,CAAC,CAAC;EAC1E,OAAOL,KAAK,CAACe,eAAe,GAAGf,KAAK,CAACe,eAAe,CAACH,KAAK,CAAC,GAAGA,KAAK;AACvE;AACA,SAASI,YAAYA,CAAChB,KAAK,EAAEI,WAAW,EAAEC,QAAQ,EAAE;EAChD;EACA,MAAMY,SAAS,GAAG,CAAC,CAAC;EACpB,MAAML,KAAK,GAAGC,QAAQ,CAACb,KAAK,EAAEI,WAAW,EAAEC,QAAQ,CAAC;EACpD,IAAIL,KAAK,CAACkB,IAAI,IAAIlB,KAAK,CAACmB,YAAY,KAAK,KAAK,EAAE;IAC5C;IACAF,SAAS,CAACG,SAAS,GAAG,KAAK;IAC3B;IACAR,KAAK,CAACS,UAAU,GACZT,KAAK,CAACU,gBAAgB,GAClBV,KAAK,CAACW,kBAAkB,GACpB,MAAM;IAClB;IACAX,KAAK,CAACY,WAAW,GACbxB,KAAK,CAACkB,IAAI,KAAK,IAAI,GACb,MAAM,GACL,OAAMlB,KAAK,CAACkB,IAAI,KAAK,GAAG,GAAG,GAAG,GAAG,GAAI,EAAC;EACrD;EACA,IAAIlB,KAAK,CAACyB,QAAQ,KAAKC,SAAS,KAC3B1B,KAAK,CAAC2B,KAAK,IAAI3B,KAAK,CAAC4B,UAAU,IAAI5B,KAAK,CAAC6B,QAAQ,CAAC,EAAE;IACrDZ,SAAS,CAACQ,QAAQ,GAAG,CAAC;EAC1B;EACAR,SAAS,CAACL,KAAK,GAAGA,KAAK;EACvB,OAAOK,SAAS;AACpB;AAEA,SAASpB,iBAAiB,EAAEmB,YAAY"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}