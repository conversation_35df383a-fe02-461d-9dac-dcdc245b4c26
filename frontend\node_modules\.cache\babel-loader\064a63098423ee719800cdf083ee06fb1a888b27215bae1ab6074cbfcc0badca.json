{"ast": null, "code": "import { createContext } from 'react';\nconst ReorderContext = createContext(null);\nexport { ReorderContext };", "map": {"version": 3, "names": ["createContext", "ReorderContext"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/context/ReorderContext.mjs"], "sourcesContent": ["import { createContext } from 'react';\n\nconst ReorderContext = createContext(null);\n\nexport { ReorderContext };\n"], "mappings": "AAAA,SAASA,aAAa,QAAQ,OAAO;AAErC,MAAMC,cAAc,GAAGD,aAAa,CAAC,IAAI,CAAC;AAE1C,SAASC,cAAc"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}