{"ast": null, "code": "import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { calcSVGTransformOrigin } from './transform-origin.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attrbutes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, _ref, options, isSVGTag, transformTemplate) {\n  let {\n    attrX,\n    attrY,\n    attrScale,\n    originX,\n    originY,\n    pathLength,\n    pathSpacing = 1,\n    pathOffset = 0,\n    // This is object creation, which we try to avoid per-frame.\n    ...latest\n  } = _ref;\n  buildHTMLStyles(state, latest, options, transformTemplate);\n  /**\n   * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n   * as normal HTML tags.\n   */\n  if (isSVGTag) {\n    if (state.style.viewBox) {\n      state.attrs.viewBox = state.style.viewBox;\n    }\n    return;\n  }\n  state.attrs = state.style;\n  state.style = {};\n  const {\n    attrs,\n    style,\n    dimensions\n  } = state;\n  /**\n   * However, we apply transforms as CSS transforms. So if we detect a transform we take it from attrs\n   * and copy it into style.\n   */\n  if (attrs.transform) {\n    if (dimensions) style.transform = attrs.transform;\n    delete attrs.transform;\n  }\n  // Parse transformOrigin\n  if (dimensions && (originX !== undefined || originY !== undefined || style.transform)) {\n    style.transformOrigin = calcSVGTransformOrigin(dimensions, originX !== undefined ? originX : 0.5, originY !== undefined ? originY : 0.5);\n  }\n  // Render attrX/attrY/attrScale as attributes\n  if (attrX !== undefined) attrs.x = attrX;\n  if (attrY !== undefined) attrs.y = attrY;\n  if (attrScale !== undefined) attrs.scale = attrScale;\n  // Build SVG path if one has been defined\n  if (pathLength !== undefined) {\n    buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n  }\n}\nexport { buildSVGAttrs };", "map": {"version": 3, "names": ["buildHTMLStyles", "calcSVGTransformOrigin", "buildSVGPath", "buildSVGAttrs", "state", "_ref", "options", "isSVGTag", "transformTemplate", "attrX", "attrY", "attrScale", "originX", "originY", "<PERSON><PERSON><PERSON><PERSON>", "pathSpacing", "pathOffset", "latest", "style", "viewBox", "attrs", "dimensions", "transform", "undefined", "transform<PERSON><PERSON>in", "x", "y", "scale"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/render/svg/utils/build-attrs.mjs"], "sourcesContent": ["import { buildHTMLStyles } from '../../html/utils/build-styles.mjs';\nimport { calcSVGTransformOrigin } from './transform-origin.mjs';\nimport { buildSVGPath } from './path.mjs';\n\n/**\n * Build SVG visual attrbutes, like cx and style.transform\n */\nfunction buildSVGAttrs(state, { attrX, attrY, attrScale, originX, originY, pathLength, pathSpacing = 1, pathOffset = 0, \n// This is object creation, which we try to avoid per-frame.\n...latest }, options, isSVGTag, transformTemplate) {\n    buildHTMLStyles(state, latest, options, transformTemplate);\n    /**\n     * For svg tags we just want to make sure viewBox is animatable and treat all the styles\n     * as normal HTML tags.\n     */\n    if (isSVGTag) {\n        if (state.style.viewBox) {\n            state.attrs.viewBox = state.style.viewBox;\n        }\n        return;\n    }\n    state.attrs = state.style;\n    state.style = {};\n    const { attrs, style, dimensions } = state;\n    /**\n     * However, we apply transforms as CSS transforms. So if we detect a transform we take it from attrs\n     * and copy it into style.\n     */\n    if (attrs.transform) {\n        if (dimensions)\n            style.transform = attrs.transform;\n        delete attrs.transform;\n    }\n    // Parse transformOrigin\n    if (dimensions &&\n        (originX !== undefined || originY !== undefined || style.transform)) {\n        style.transformOrigin = calcSVGTransformOrigin(dimensions, originX !== undefined ? originX : 0.5, originY !== undefined ? originY : 0.5);\n    }\n    // Render attrX/attrY/attrScale as attributes\n    if (attrX !== undefined)\n        attrs.x = attrX;\n    if (attrY !== undefined)\n        attrs.y = attrY;\n    if (attrScale !== undefined)\n        attrs.scale = attrScale;\n    // Build SVG path if one has been defined\n    if (pathLength !== undefined) {\n        buildSVGPath(attrs, pathLength, pathSpacing, pathOffset, false);\n    }\n}\n\nexport { buildSVGAttrs };\n"], "mappings": "AAAA,SAASA,eAAe,QAAQ,mCAAmC;AACnE,SAASC,sBAAsB,QAAQ,wBAAwB;AAC/D,SAASC,YAAY,QAAQ,YAAY;;AAEzC;AACA;AACA;AACA,SAASC,aAAaA,CAACC,KAAK,EAAAC,IAAA,EAEfC,OAAO,EAAEC,QAAQ,EAAEC,iBAAiB,EAAE;EAAA,IAFrB;IAAEC,KAAK;IAAEC,KAAK;IAAEC,SAAS;IAAEC,OAAO;IAAEC,OAAO;IAAEC,UAAU;IAAEC,WAAW,GAAG,CAAC;IAAEC,UAAU,GAAG,CAAC;IACtH;IACA,GAAGC;EAAO,CAAC,GAAAZ,IAAA;EACPL,eAAe,CAACI,KAAK,EAAEa,MAAM,EAAEX,OAAO,EAAEE,iBAAiB,CAAC;EAC1D;AACJ;AACA;AACA;EACI,IAAID,QAAQ,EAAE;IACV,IAAIH,KAAK,CAACc,KAAK,CAACC,OAAO,EAAE;MACrBf,KAAK,CAACgB,KAAK,CAACD,OAAO,GAAGf,KAAK,CAACc,KAAK,CAACC,OAAO;IAC7C;IACA;EACJ;EACAf,KAAK,CAACgB,KAAK,GAAGhB,KAAK,CAACc,KAAK;EACzBd,KAAK,CAACc,KAAK,GAAG,CAAC,CAAC;EAChB,MAAM;IAAEE,KAAK;IAAEF,KAAK;IAAEG;EAAW,CAAC,GAAGjB,KAAK;EAC1C;AACJ;AACA;AACA;EACI,IAAIgB,KAAK,CAACE,SAAS,EAAE;IACjB,IAAID,UAAU,EACVH,KAAK,CAACI,SAAS,GAAGF,KAAK,CAACE,SAAS;IACrC,OAAOF,KAAK,CAACE,SAAS;EAC1B;EACA;EACA,IAAID,UAAU,KACTT,OAAO,KAAKW,SAAS,IAAIV,OAAO,KAAKU,SAAS,IAAIL,KAAK,CAACI,SAAS,CAAC,EAAE;IACrEJ,KAAK,CAACM,eAAe,GAAGvB,sBAAsB,CAACoB,UAAU,EAAET,OAAO,KAAKW,SAAS,GAAGX,OAAO,GAAG,GAAG,EAAEC,OAAO,KAAKU,SAAS,GAAGV,OAAO,GAAG,GAAG,CAAC;EAC5I;EACA;EACA,IAAIJ,KAAK,KAAKc,SAAS,EACnBH,KAAK,CAACK,CAAC,GAAGhB,KAAK;EACnB,IAAIC,KAAK,KAAKa,SAAS,EACnBH,KAAK,CAACM,CAAC,GAAGhB,KAAK;EACnB,IAAIC,SAAS,KAAKY,SAAS,EACvBH,KAAK,CAACO,KAAK,GAAGhB,SAAS;EAC3B;EACA,IAAIG,UAAU,KAAKS,SAAS,EAAE;IAC1BrB,YAAY,CAACkB,KAAK,EAAEN,UAAU,EAAEC,WAAW,EAAEC,UAAU,EAAE,KAAK,CAAC;EACnE;AACJ;AAEA,SAASb,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}