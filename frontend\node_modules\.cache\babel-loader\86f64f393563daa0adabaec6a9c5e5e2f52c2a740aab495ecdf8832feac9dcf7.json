{"ast": null, "code": "/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/engine-v8-version');\nvar fails = require('../internals/fails');\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol();\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  return !String(symbol) || !(Object(symbol) instanceof Symbol) ||\n  // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n  !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});", "map": {"version": 3, "names": ["V8_VERSION", "require", "fails", "module", "exports", "Object", "getOwnPropertySymbols", "symbol", "Symbol", "String", "sham"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/core-js-pure/internals/symbol-constructor-detection.js"], "sourcesContent": ["/* eslint-disable es/no-symbol -- required for testing */\nvar V8_VERSION = require('../internals/engine-v8-version');\nvar fails = require('../internals/fails');\n\n// eslint-disable-next-line es/no-object-getownpropertysymbols -- required for testing\nmodule.exports = !!Object.getOwnPropertySymbols && !fails(function () {\n  var symbol = Symbol();\n  // Chrome 38 Symbol has incorrect toString conversion\n  // `get-own-property-symbols` polyfill symbols converted to object are not Symbol instances\n  return !String(symbol) || !(Object(symbol) instanceof Symbol) ||\n    // Chrome 38-40 symbols are not inherited from DOM collections prototypes to instances\n    !Symbol.sham && V8_VERSION && V8_VERSION < 41;\n});\n"], "mappings": "AAAA;AACA,IAAIA,UAAU,GAAGC,OAAO,CAAC,gCAAgC,CAAC;AAC1D,IAAIC,KAAK,GAAGD,OAAO,CAAC,oBAAoB,CAAC;;AAEzC;AACAE,MAAM,CAACC,OAAO,GAAG,CAAC,CAACC,MAAM,CAACC,qBAAqB,IAAI,CAACJ,KAAK,CAAC,YAAY;EACpE,IAAIK,MAAM,GAAGC,MAAM,EAAE;EACrB;EACA;EACA,OAAO,CAACC,MAAM,CAACF,MAAM,CAAC,IAAI,EAAEF,MAAM,CAACE,MAAM,CAAC,YAAYC,MAAM,CAAC;EAC3D;EACA,CAACA,MAAM,CAACE,IAAI,IAAIV,UAAU,IAAIA,UAAU,GAAG,EAAE;AACjD,CAAC,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}