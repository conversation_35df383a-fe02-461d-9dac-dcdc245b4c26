{"ast": null, "code": "var global = require('../internals/global');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(global, key, {\n      value: value,\n      configurable: true,\n      writable: true\n    });\n  } catch (error) {\n    global[key] = value;\n  }\n  return value;\n};", "map": {"version": 3, "names": ["global", "require", "defineProperty", "Object", "module", "exports", "key", "value", "configurable", "writable", "error"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/core-js-pure/internals/define-global-property.js"], "sourcesContent": ["var global = require('../internals/global');\n\n// eslint-disable-next-line es/no-object-defineproperty -- safe\nvar defineProperty = Object.defineProperty;\n\nmodule.exports = function (key, value) {\n  try {\n    defineProperty(global, key, { value: value, configurable: true, writable: true });\n  } catch (error) {\n    global[key] = value;\n  } return value;\n};\n"], "mappings": "AAAA,IAAIA,MAAM,GAAGC,OAAO,CAAC,qBAAqB,CAAC;;AAE3C;AACA,IAAIC,cAAc,GAAGC,MAAM,CAACD,cAAc;AAE1CE,MAAM,CAACC,OAAO,GAAG,UAAUC,GAAG,EAAEC,KAAK,EAAE;EACrC,IAAI;IACFL,cAAc,CAACF,MAAM,EAAEM,GAAG,EAAE;MAAEC,KAAK,EAAEA,KAAK;MAAEC,YAAY,EAAE,IAAI;MAAEC,QAAQ,EAAE;IAAK,CAAC,CAAC;EACnF,CAAC,CAAC,OAAOC,KAAK,EAAE;IACdV,MAAM,CAACM,GAAG,CAAC,GAAGC,KAAK;EACrB;EAAE,OAAOA,KAAK;AAChB,CAAC"}, "metadata": {}, "sourceType": "script", "externalDependencies": []}