{"ast": null, "code": "import httpService from './httpService';\nexport const fetchCoupons = () => httpService.get('/api/coupons/');\nexport const createCoupon = data => httpService.post('/api/coupons/', data);\nexport const updateCoupon = (id, data) => httpService.put(`/api/coupons/${id}/`, data);\nexport const deleteCoupon = id => httpService.delete(`/api/coupons/${id}/`);", "map": {"version": 3, "names": ["httpService", "fetchCoupons", "get", "createCoupon", "data", "post", "updateCoupon", "id", "put", "deleteCoupon", "delete"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/src/services/couponService.js"], "sourcesContent": ["import httpService from './httpService';\n\nexport const fetchCoupons = () => httpService.get('/api/coupons/');\nexport const createCoupon = (data) => httpService.post('/api/coupons/', data);\nexport const updateCoupon = (id, data) => httpService.put(`/api/coupons/${id}/`, data);\nexport const deleteCoupon = (id) => httpService.delete(`/api/coupons/${id}/`);"], "mappings": "AAAA,OAAOA,WAAW,MAAM,eAAe;AAEvC,OAAO,MAAMC,YAAY,GAAGA,CAAA,KAAMD,WAAW,CAACE,GAAG,CAAC,eAAe,CAAC;AAClE,OAAO,MAAMC,YAAY,GAAIC,IAAI,IAAKJ,WAAW,CAACK,IAAI,CAAC,eAAe,EAAED,IAAI,CAAC;AAC7E,OAAO,MAAME,YAAY,GAAGA,CAACC,EAAE,EAAEH,IAAI,KAAKJ,WAAW,CAACQ,GAAG,CAAE,gBAAeD,EAAG,GAAE,EAAEH,IAAI,CAAC;AACtF,OAAO,MAAMK,YAAY,GAAIF,EAAE,IAAKP,WAAW,CAACU,MAAM,CAAE,gBAAeH,EAAG,GAAE,CAAC"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}