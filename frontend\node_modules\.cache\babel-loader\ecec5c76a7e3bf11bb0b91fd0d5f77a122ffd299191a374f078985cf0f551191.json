{"ast": null, "code": "import { createGeneratorEasing } from '../../easing/utils/create-generator-easing.mjs';\nimport { resolveElements } from '../../render/dom/utils/resolve-element.mjs';\nimport { defaultOffset } from '../../utils/offsets/default.mjs';\nimport { fillOffset } from '../../utils/offsets/fill.mjs';\nimport { progress } from '../../utils/progress.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { compareByTime } from './utils/sort.mjs';\nconst defaultSegmentEasing = \"easeInOut\";\nfunction createAnimationsFromSequence(sequence) {\n  let {\n    defaultTransition = {},\n    ...sequenceTransition\n  } = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : {};\n  let scope = arguments.length > 2 ? arguments[2] : undefined;\n  const defaultDuration = defaultTransition.duration || 0.3;\n  const animationDefinitions = new Map();\n  const sequences = new Map();\n  const elementCache = {};\n  const timeLabels = new Map();\n  let prevTime = 0;\n  let currentTime = 0;\n  let totalDuration = 0;\n  /**\n   * Build the timeline by mapping over the sequence array and converting\n   * the definitions into keyframes and offsets with absolute time values.\n   * These will later get converted into relative offsets in a second pass.\n   */\n  for (let i = 0; i < sequence.length; i++) {\n    const segment = sequence[i];\n    /**\n     * If this is a timeline label, mark it and skip the rest of this iteration.\n     */\n    if (typeof segment === \"string\") {\n      timeLabels.set(segment, currentTime);\n      continue;\n    } else if (!Array.isArray(segment)) {\n      timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n      continue;\n    }\n    let [subject, keyframes, transition = {}] = segment;\n    /**\n     * If a relative or absolute time value has been specified we need to resolve\n     * it in relation to the currentTime.\n     */\n    if (transition.at !== undefined) {\n      currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n    }\n    /**\n     * Keep track of the maximum duration in this definition. This will be\n     * applied to currentTime once the definition has been parsed.\n     */\n    let maxDuration = 0;\n    const resolveValueSequence = function (valueKeyframes, valueTransition, valueSequence) {\n      let elementIndex = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0;\n      let numElements = arguments.length > 4 && arguments[4] !== undefined ? arguments[4] : 0;\n      const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n      const {\n        delay = 0,\n        times = defaultOffset(valueKeyframesAsList),\n        type = \"keyframes\",\n        ...remainingTransition\n      } = valueTransition;\n      let {\n        ease = defaultTransition.ease || \"easeOut\",\n        duration\n      } = valueTransition;\n      /**\n       * Resolve stagger() if defined.\n       */\n      const calculatedDelay = typeof delay === \"function\" ? delay(elementIndex, numElements) : delay;\n      /**\n       * If this animation should and can use a spring, generate a spring easing function.\n       */\n      const numKeyframes = valueKeyframesAsList.length;\n      if (numKeyframes <= 2 && type === \"spring\") {\n        /**\n         * As we're creating an easing function from a spring,\n         * ideally we want to generate it using the real distance\n         * between the two keyframes. However this isn't always\n         * possible - in these situations we use 0-100.\n         */\n        let absoluteDelta = 100;\n        if (numKeyframes === 2 && isNumberKeyframesArray(valueKeyframesAsList)) {\n          const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n          absoluteDelta = Math.abs(delta);\n        }\n        const springTransition = {\n          ...remainingTransition\n        };\n        if (duration !== undefined) {\n          springTransition.duration = secondsToMilliseconds(duration);\n        }\n        const springEasing = createGeneratorEasing(springTransition, absoluteDelta);\n        ease = springEasing.ease;\n        duration = springEasing.duration;\n      }\n      duration !== null && duration !== void 0 ? duration : duration = defaultDuration;\n      const startTime = currentTime + calculatedDelay;\n      const targetTime = startTime + duration;\n      /**\n       * If there's only one time offset of 0, fill in a second with length 1\n       */\n      if (times.length === 1 && times[0] === 0) {\n        times[1] = 1;\n      }\n      /**\n       * Fill out if offset if fewer offsets than keyframes\n       */\n      const remainder = times.length - valueKeyframesAsList.length;\n      remainder > 0 && fillOffset(times, remainder);\n      /**\n       * If only one value has been set, ie [1], push a null to the start of\n       * the keyframe array. This will let us mark a keyframe at this point\n       * that will later be hydrated with the previous value.\n       */\n      valueKeyframesAsList.length === 1 && valueKeyframesAsList.unshift(null);\n      /**\n       * Add keyframes, mapping offsets to absolute time.\n       */\n      addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n      maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n      totalDuration = Math.max(targetTime, totalDuration);\n    };\n    if (isMotionValue(subject)) {\n      const subjectSequence = getSubjectSequence(subject, sequences);\n      resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n    } else {\n      /**\n       * Find all the elements specified in the definition and parse value\n       * keyframes from their timeline definitions.\n       */\n      const elements = resolveElements(subject, scope, elementCache);\n      const numElements = elements.length;\n      /**\n       * For every element in this segment, process the defined values.\n       */\n      for (let elementIndex = 0; elementIndex < numElements; elementIndex++) {\n        /**\n         * Cast necessary, but we know these are of this type\n         */\n        keyframes = keyframes;\n        transition = transition;\n        const element = elements[elementIndex];\n        const subjectSequence = getSubjectSequence(element, sequences);\n        for (const key in keyframes) {\n          resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), elementIndex, numElements);\n        }\n      }\n    }\n    prevTime = currentTime;\n    currentTime += maxDuration;\n  }\n  /**\n   * For every element and value combination create a new animation.\n   */\n  sequences.forEach((valueSequences, element) => {\n    for (const key in valueSequences) {\n      const valueSequence = valueSequences[key];\n      /**\n       * Arrange all the keyframes in ascending time order.\n       */\n      valueSequence.sort(compareByTime);\n      const keyframes = [];\n      const valueOffset = [];\n      const valueEasing = [];\n      /**\n       * For each keyframe, translate absolute times into\n       * relative offsets based on the total duration of the timeline.\n       */\n      for (let i = 0; i < valueSequence.length; i++) {\n        const {\n          at,\n          value,\n          easing\n        } = valueSequence[i];\n        keyframes.push(value);\n        valueOffset.push(progress(0, totalDuration, at));\n        valueEasing.push(easing || \"easeOut\");\n      }\n      /**\n       * If the first keyframe doesn't land on offset: 0\n       * provide one by duplicating the initial keyframe. This ensures\n       * it snaps to the first keyframe when the animation starts.\n       */\n      if (valueOffset[0] !== 0) {\n        valueOffset.unshift(0);\n        keyframes.unshift(keyframes[0]);\n        valueEasing.unshift(defaultSegmentEasing);\n      }\n      /**\n       * If the last keyframe doesn't land on offset: 1\n       * provide one with a null wildcard value. This will ensure it\n       * stays static until the end of the animation.\n       */\n      if (valueOffset[valueOffset.length - 1] !== 1) {\n        valueOffset.push(1);\n        keyframes.push(null);\n      }\n      if (!animationDefinitions.has(element)) {\n        animationDefinitions.set(element, {\n          keyframes: {},\n          transition: {}\n        });\n      }\n      const definition = animationDefinitions.get(element);\n      definition.keyframes[key] = keyframes;\n      definition.transition[key] = {\n        ...defaultTransition,\n        duration: totalDuration,\n        ease: valueEasing,\n        times: valueOffset,\n        ...sequenceTransition\n      };\n    }\n  });\n  return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n  !sequences.has(subject) && sequences.set(subject, {});\n  return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n  if (!sequences[name]) sequences[name] = [];\n  return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n  return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n  return transition[key] ? {\n    ...transition,\n    ...transition[key]\n  } : {\n    ...transition\n  };\n}\nconst isNumber = keyframe => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = keyframes => keyframes.every(isNumber);\nexport { createAnimationsFromSequence, getValueTransition };", "map": {"version": 3, "names": ["createGeneratorEasing", "resolveElements", "defaultOffset", "fillOffset", "progress", "secondsToMilliseconds", "isMotionValue", "calcNextTime", "addKeyframes", "compareByTime", "defaultSegmentEasing", "createAnimationsFromSequence", "sequence", "defaultTransition", "sequenceTransition", "arguments", "length", "undefined", "scope", "defaultDuration", "duration", "animationDefinitions", "Map", "sequences", "elementCache", "time<PERSON><PERSON><PERSON>", "prevTime", "currentTime", "totalDuration", "i", "segment", "set", "Array", "isArray", "name", "at", "subject", "keyframes", "transition", "maxDuration", "resolveValueSequence", "valueKeyframes", "valueTransition", "valueSequence", "elementIndex", "numElements", "valueKeyframesAsList", "keyframesAsList", "delay", "times", "type", "remainingTransition", "ease", "calculatedDelay", "numKeyframes", "absoluteDelta", "isNumberKeyframesArray", "delta", "Math", "abs", "springTransition", "springEasing", "startTime", "targetTime", "remainder", "unshift", "max", "subjectSequence", "getSubjectSequence", "getValueSequence", "elements", "element", "key", "getValueTransition", "for<PERSON>ach", "valueSequences", "sort", "valueOffset", "valueEasing", "value", "easing", "push", "has", "definition", "get", "isNumber", "keyframe", "every"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/animation/sequence/create.mjs"], "sourcesContent": ["import { createGeneratorEasing } from '../../easing/utils/create-generator-easing.mjs';\nimport { resolveElements } from '../../render/dom/utils/resolve-element.mjs';\nimport { defaultOffset } from '../../utils/offsets/default.mjs';\nimport { fillOffset } from '../../utils/offsets/fill.mjs';\nimport { progress } from '../../utils/progress.mjs';\nimport { secondsToMilliseconds } from '../../utils/time-conversion.mjs';\nimport { isMotionValue } from '../../value/utils/is-motion-value.mjs';\nimport { calcNextTime } from './utils/calc-time.mjs';\nimport { addKeyframes } from './utils/edit.mjs';\nimport { compareByTime } from './utils/sort.mjs';\n\nconst defaultSegmentEasing = \"easeInOut\";\nfunction createAnimationsFromSequence(sequence, { defaultTransition = {}, ...sequenceTransition } = {}, scope) {\n    const defaultDuration = defaultTransition.duration || 0.3;\n    const animationDefinitions = new Map();\n    const sequences = new Map();\n    const elementCache = {};\n    const timeLabels = new Map();\n    let prevTime = 0;\n    let currentTime = 0;\n    let totalDuration = 0;\n    /**\n     * Build the timeline by mapping over the sequence array and converting\n     * the definitions into keyframes and offsets with absolute time values.\n     * These will later get converted into relative offsets in a second pass.\n     */\n    for (let i = 0; i < sequence.length; i++) {\n        const segment = sequence[i];\n        /**\n         * If this is a timeline label, mark it and skip the rest of this iteration.\n         */\n        if (typeof segment === \"string\") {\n            timeLabels.set(segment, currentTime);\n            continue;\n        }\n        else if (!Array.isArray(segment)) {\n            timeLabels.set(segment.name, calcNextTime(currentTime, segment.at, prevTime, timeLabels));\n            continue;\n        }\n        let [subject, keyframes, transition = {}] = segment;\n        /**\n         * If a relative or absolute time value has been specified we need to resolve\n         * it in relation to the currentTime.\n         */\n        if (transition.at !== undefined) {\n            currentTime = calcNextTime(currentTime, transition.at, prevTime, timeLabels);\n        }\n        /**\n         * Keep track of the maximum duration in this definition. This will be\n         * applied to currentTime once the definition has been parsed.\n         */\n        let maxDuration = 0;\n        const resolveValueSequence = (valueKeyframes, valueTransition, valueSequence, elementIndex = 0, numElements = 0) => {\n            const valueKeyframesAsList = keyframesAsList(valueKeyframes);\n            const { delay = 0, times = defaultOffset(valueKeyframesAsList), type = \"keyframes\", ...remainingTransition } = valueTransition;\n            let { ease = defaultTransition.ease || \"easeOut\", duration } = valueTransition;\n            /**\n             * Resolve stagger() if defined.\n             */\n            const calculatedDelay = typeof delay === \"function\"\n                ? delay(elementIndex, numElements)\n                : delay;\n            /**\n             * If this animation should and can use a spring, generate a spring easing function.\n             */\n            const numKeyframes = valueKeyframesAsList.length;\n            if (numKeyframes <= 2 && type === \"spring\") {\n                /**\n                 * As we're creating an easing function from a spring,\n                 * ideally we want to generate it using the real distance\n                 * between the two keyframes. However this isn't always\n                 * possible - in these situations we use 0-100.\n                 */\n                let absoluteDelta = 100;\n                if (numKeyframes === 2 &&\n                    isNumberKeyframesArray(valueKeyframesAsList)) {\n                    const delta = valueKeyframesAsList[1] - valueKeyframesAsList[0];\n                    absoluteDelta = Math.abs(delta);\n                }\n                const springTransition = { ...remainingTransition };\n                if (duration !== undefined) {\n                    springTransition.duration = secondsToMilliseconds(duration);\n                }\n                const springEasing = createGeneratorEasing(springTransition, absoluteDelta);\n                ease = springEasing.ease;\n                duration = springEasing.duration;\n            }\n            duration !== null && duration !== void 0 ? duration : (duration = defaultDuration);\n            const startTime = currentTime + calculatedDelay;\n            const targetTime = startTime + duration;\n            /**\n             * If there's only one time offset of 0, fill in a second with length 1\n             */\n            if (times.length === 1 && times[0] === 0) {\n                times[1] = 1;\n            }\n            /**\n             * Fill out if offset if fewer offsets than keyframes\n             */\n            const remainder = times.length - valueKeyframesAsList.length;\n            remainder > 0 && fillOffset(times, remainder);\n            /**\n             * If only one value has been set, ie [1], push a null to the start of\n             * the keyframe array. This will let us mark a keyframe at this point\n             * that will later be hydrated with the previous value.\n             */\n            valueKeyframesAsList.length === 1 &&\n                valueKeyframesAsList.unshift(null);\n            /**\n             * Add keyframes, mapping offsets to absolute time.\n             */\n            addKeyframes(valueSequence, valueKeyframesAsList, ease, times, startTime, targetTime);\n            maxDuration = Math.max(calculatedDelay + duration, maxDuration);\n            totalDuration = Math.max(targetTime, totalDuration);\n        };\n        if (isMotionValue(subject)) {\n            const subjectSequence = getSubjectSequence(subject, sequences);\n            resolveValueSequence(keyframes, transition, getValueSequence(\"default\", subjectSequence));\n        }\n        else {\n            /**\n             * Find all the elements specified in the definition and parse value\n             * keyframes from their timeline definitions.\n             */\n            const elements = resolveElements(subject, scope, elementCache);\n            const numElements = elements.length;\n            /**\n             * For every element in this segment, process the defined values.\n             */\n            for (let elementIndex = 0; elementIndex < numElements; elementIndex++) {\n                /**\n                 * Cast necessary, but we know these are of this type\n                 */\n                keyframes = keyframes;\n                transition = transition;\n                const element = elements[elementIndex];\n                const subjectSequence = getSubjectSequence(element, sequences);\n                for (const key in keyframes) {\n                    resolveValueSequence(keyframes[key], getValueTransition(transition, key), getValueSequence(key, subjectSequence), elementIndex, numElements);\n                }\n            }\n        }\n        prevTime = currentTime;\n        currentTime += maxDuration;\n    }\n    /**\n     * For every element and value combination create a new animation.\n     */\n    sequences.forEach((valueSequences, element) => {\n        for (const key in valueSequences) {\n            const valueSequence = valueSequences[key];\n            /**\n             * Arrange all the keyframes in ascending time order.\n             */\n            valueSequence.sort(compareByTime);\n            const keyframes = [];\n            const valueOffset = [];\n            const valueEasing = [];\n            /**\n             * For each keyframe, translate absolute times into\n             * relative offsets based on the total duration of the timeline.\n             */\n            for (let i = 0; i < valueSequence.length; i++) {\n                const { at, value, easing } = valueSequence[i];\n                keyframes.push(value);\n                valueOffset.push(progress(0, totalDuration, at));\n                valueEasing.push(easing || \"easeOut\");\n            }\n            /**\n             * If the first keyframe doesn't land on offset: 0\n             * provide one by duplicating the initial keyframe. This ensures\n             * it snaps to the first keyframe when the animation starts.\n             */\n            if (valueOffset[0] !== 0) {\n                valueOffset.unshift(0);\n                keyframes.unshift(keyframes[0]);\n                valueEasing.unshift(defaultSegmentEasing);\n            }\n            /**\n             * If the last keyframe doesn't land on offset: 1\n             * provide one with a null wildcard value. This will ensure it\n             * stays static until the end of the animation.\n             */\n            if (valueOffset[valueOffset.length - 1] !== 1) {\n                valueOffset.push(1);\n                keyframes.push(null);\n            }\n            if (!animationDefinitions.has(element)) {\n                animationDefinitions.set(element, {\n                    keyframes: {},\n                    transition: {},\n                });\n            }\n            const definition = animationDefinitions.get(element);\n            definition.keyframes[key] = keyframes;\n            definition.transition[key] = {\n                ...defaultTransition,\n                duration: totalDuration,\n                ease: valueEasing,\n                times: valueOffset,\n                ...sequenceTransition,\n            };\n        }\n    });\n    return animationDefinitions;\n}\nfunction getSubjectSequence(subject, sequences) {\n    !sequences.has(subject) && sequences.set(subject, {});\n    return sequences.get(subject);\n}\nfunction getValueSequence(name, sequences) {\n    if (!sequences[name])\n        sequences[name] = [];\n    return sequences[name];\n}\nfunction keyframesAsList(keyframes) {\n    return Array.isArray(keyframes) ? keyframes : [keyframes];\n}\nfunction getValueTransition(transition, key) {\n    return transition[key]\n        ? { ...transition, ...transition[key] }\n        : { ...transition };\n}\nconst isNumber = (keyframe) => typeof keyframe === \"number\";\nconst isNumberKeyframesArray = (keyframes) => keyframes.every(isNumber);\n\nexport { createAnimationsFromSequence, getValueTransition };\n"], "mappings": "AAAA,SAASA,qBAAqB,QAAQ,gDAAgD;AACtF,SAASC,eAAe,QAAQ,4CAA4C;AAC5E,SAASC,aAAa,QAAQ,iCAAiC;AAC/D,SAASC,UAAU,QAAQ,8BAA8B;AACzD,SAASC,QAAQ,QAAQ,0BAA0B;AACnD,SAASC,qBAAqB,QAAQ,iCAAiC;AACvE,SAASC,aAAa,QAAQ,uCAAuC;AACrE,SAASC,YAAY,QAAQ,uBAAuB;AACpD,SAASC,YAAY,QAAQ,kBAAkB;AAC/C,SAASC,aAAa,QAAQ,kBAAkB;AAEhD,MAAMC,oBAAoB,GAAG,WAAW;AACxC,SAASC,4BAA4BA,CAACC,QAAQ,EAAiE;EAAA,IAA/D;IAAEC,iBAAiB,GAAG,CAAC,CAAC;IAAE,GAAGC;EAAmB,CAAC,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC,CAAC;EAAA,IAAEG,KAAK,GAAAH,SAAA,CAAAC,MAAA,OAAAD,SAAA,MAAAE,SAAA;EACzG,MAAME,eAAe,GAAGN,iBAAiB,CAACO,QAAQ,IAAI,GAAG;EACzD,MAAMC,oBAAoB,GAAG,IAAIC,GAAG,EAAE;EACtC,MAAMC,SAAS,GAAG,IAAID,GAAG,EAAE;EAC3B,MAAME,YAAY,GAAG,CAAC,CAAC;EACvB,MAAMC,UAAU,GAAG,IAAIH,GAAG,EAAE;EAC5B,IAAII,QAAQ,GAAG,CAAC;EAChB,IAAIC,WAAW,GAAG,CAAC;EACnB,IAAIC,aAAa,GAAG,CAAC;EACrB;AACJ;AACA;AACA;AACA;EACI,KAAK,IAAIC,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGjB,QAAQ,CAACI,MAAM,EAAEa,CAAC,EAAE,EAAE;IACtC,MAAMC,OAAO,GAAGlB,QAAQ,CAACiB,CAAC,CAAC;IAC3B;AACR;AACA;IACQ,IAAI,OAAOC,OAAO,KAAK,QAAQ,EAAE;MAC7BL,UAAU,CAACM,GAAG,CAACD,OAAO,EAAEH,WAAW,CAAC;MACpC;IACJ,CAAC,MACI,IAAI,CAACK,KAAK,CAACC,OAAO,CAACH,OAAO,CAAC,EAAE;MAC9BL,UAAU,CAACM,GAAG,CAACD,OAAO,CAACI,IAAI,EAAE3B,YAAY,CAACoB,WAAW,EAAEG,OAAO,CAACK,EAAE,EAAET,QAAQ,EAAED,UAAU,CAAC,CAAC;MACzF;IACJ;IACA,IAAI,CAACW,OAAO,EAAEC,SAAS,EAAEC,UAAU,GAAG,CAAC,CAAC,CAAC,GAAGR,OAAO;IACnD;AACR;AACA;AACA;IACQ,IAAIQ,UAAU,CAACH,EAAE,KAAKlB,SAAS,EAAE;MAC7BU,WAAW,GAAGpB,YAAY,CAACoB,WAAW,EAAEW,UAAU,CAACH,EAAE,EAAET,QAAQ,EAAED,UAAU,CAAC;IAChF;IACA;AACR;AACA;AACA;IACQ,IAAIc,WAAW,GAAG,CAAC;IACnB,MAAMC,oBAAoB,GAAG,SAAAA,CAACC,cAAc,EAAEC,eAAe,EAAEC,aAAa,EAAwC;MAAA,IAAtCC,YAAY,GAAA7B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAAA,IAAE8B,WAAW,GAAA9B,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;MAC3G,MAAM+B,oBAAoB,GAAGC,eAAe,CAACN,cAAc,CAAC;MAC5D,MAAM;QAAEO,KAAK,GAAG,CAAC;QAAEC,KAAK,GAAG/C,aAAa,CAAC4C,oBAAoB,CAAC;QAAEI,IAAI,GAAG,WAAW;QAAE,GAAGC;MAAoB,CAAC,GAAGT,eAAe;MAC9H,IAAI;QAAEU,IAAI,GAAGvC,iBAAiB,CAACuC,IAAI,IAAI,SAAS;QAAEhC;MAAS,CAAC,GAAGsB,eAAe;MAC9E;AACZ;AACA;MACY,MAAMW,eAAe,GAAG,OAAOL,KAAK,KAAK,UAAU,GAC7CA,KAAK,CAACJ,YAAY,EAAEC,WAAW,CAAC,GAChCG,KAAK;MACX;AACZ;AACA;MACY,MAAMM,YAAY,GAAGR,oBAAoB,CAAC9B,MAAM;MAChD,IAAIsC,YAAY,IAAI,CAAC,IAAIJ,IAAI,KAAK,QAAQ,EAAE;QACxC;AAChB;AACA;AACA;AACA;AACA;QACgB,IAAIK,aAAa,GAAG,GAAG;QACvB,IAAID,YAAY,KAAK,CAAC,IAClBE,sBAAsB,CAACV,oBAAoB,CAAC,EAAE;UAC9C,MAAMW,KAAK,GAAGX,oBAAoB,CAAC,CAAC,CAAC,GAAGA,oBAAoB,CAAC,CAAC,CAAC;UAC/DS,aAAa,GAAGG,IAAI,CAACC,GAAG,CAACF,KAAK,CAAC;QACnC;QACA,MAAMG,gBAAgB,GAAG;UAAE,GAAGT;QAAoB,CAAC;QACnD,IAAI/B,QAAQ,KAAKH,SAAS,EAAE;UACxB2C,gBAAgB,CAACxC,QAAQ,GAAGf,qBAAqB,CAACe,QAAQ,CAAC;QAC/D;QACA,MAAMyC,YAAY,GAAG7D,qBAAqB,CAAC4D,gBAAgB,EAAEL,aAAa,CAAC;QAC3EH,IAAI,GAAGS,YAAY,CAACT,IAAI;QACxBhC,QAAQ,GAAGyC,YAAY,CAACzC,QAAQ;MACpC;MACAA,QAAQ,KAAK,IAAI,IAAIA,QAAQ,KAAK,KAAK,CAAC,GAAGA,QAAQ,GAAIA,QAAQ,GAAGD,eAAgB;MAClF,MAAM2C,SAAS,GAAGnC,WAAW,GAAG0B,eAAe;MAC/C,MAAMU,UAAU,GAAGD,SAAS,GAAG1C,QAAQ;MACvC;AACZ;AACA;MACY,IAAI6B,KAAK,CAACjC,MAAM,KAAK,CAAC,IAAIiC,KAAK,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtCA,KAAK,CAAC,CAAC,CAAC,GAAG,CAAC;MAChB;MACA;AACZ;AACA;MACY,MAAMe,SAAS,GAAGf,KAAK,CAACjC,MAAM,GAAG8B,oBAAoB,CAAC9B,MAAM;MAC5DgD,SAAS,GAAG,CAAC,IAAI7D,UAAU,CAAC8C,KAAK,EAAEe,SAAS,CAAC;MAC7C;AACZ;AACA;AACA;AACA;MACYlB,oBAAoB,CAAC9B,MAAM,KAAK,CAAC,IAC7B8B,oBAAoB,CAACmB,OAAO,CAAC,IAAI,CAAC;MACtC;AACZ;AACA;MACYzD,YAAY,CAACmC,aAAa,EAAEG,oBAAoB,EAAEM,IAAI,EAAEH,KAAK,EAAEa,SAAS,EAAEC,UAAU,CAAC;MACrFxB,WAAW,GAAGmB,IAAI,CAACQ,GAAG,CAACb,eAAe,GAAGjC,QAAQ,EAAEmB,WAAW,CAAC;MAC/DX,aAAa,GAAG8B,IAAI,CAACQ,GAAG,CAACH,UAAU,EAAEnC,aAAa,CAAC;IACvD,CAAC;IACD,IAAItB,aAAa,CAAC8B,OAAO,CAAC,EAAE;MACxB,MAAM+B,eAAe,GAAGC,kBAAkB,CAAChC,OAAO,EAAEb,SAAS,CAAC;MAC9DiB,oBAAoB,CAACH,SAAS,EAAEC,UAAU,EAAE+B,gBAAgB,CAAC,SAAS,EAAEF,eAAe,CAAC,CAAC;IAC7F,CAAC,MACI;MACD;AACZ;AACA;AACA;MACY,MAAMG,QAAQ,GAAGrE,eAAe,CAACmC,OAAO,EAAElB,KAAK,EAAEM,YAAY,CAAC;MAC9D,MAAMqB,WAAW,GAAGyB,QAAQ,CAACtD,MAAM;MACnC;AACZ;AACA;MACY,KAAK,IAAI4B,YAAY,GAAG,CAAC,EAAEA,YAAY,GAAGC,WAAW,EAAED,YAAY,EAAE,EAAE;QACnE;AAChB;AACA;QACgBP,SAAS,GAAGA,SAAS;QACrBC,UAAU,GAAGA,UAAU;QACvB,MAAMiC,OAAO,GAAGD,QAAQ,CAAC1B,YAAY,CAAC;QACtC,MAAMuB,eAAe,GAAGC,kBAAkB,CAACG,OAAO,EAAEhD,SAAS,CAAC;QAC9D,KAAK,MAAMiD,GAAG,IAAInC,SAAS,EAAE;UACzBG,oBAAoB,CAACH,SAAS,CAACmC,GAAG,CAAC,EAAEC,kBAAkB,CAACnC,UAAU,EAAEkC,GAAG,CAAC,EAAEH,gBAAgB,CAACG,GAAG,EAAEL,eAAe,CAAC,EAAEvB,YAAY,EAAEC,WAAW,CAAC;QAChJ;MACJ;IACJ;IACAnB,QAAQ,GAAGC,WAAW;IACtBA,WAAW,IAAIY,WAAW;EAC9B;EACA;AACJ;AACA;EACIhB,SAAS,CAACmD,OAAO,CAAC,CAACC,cAAc,EAAEJ,OAAO,KAAK;IAC3C,KAAK,MAAMC,GAAG,IAAIG,cAAc,EAAE;MAC9B,MAAMhC,aAAa,GAAGgC,cAAc,CAACH,GAAG,CAAC;MACzC;AACZ;AACA;MACY7B,aAAa,CAACiC,IAAI,CAACnE,aAAa,CAAC;MACjC,MAAM4B,SAAS,GAAG,EAAE;MACpB,MAAMwC,WAAW,GAAG,EAAE;MACtB,MAAMC,WAAW,GAAG,EAAE;MACtB;AACZ;AACA;AACA;MACY,KAAK,IAAIjD,CAAC,GAAG,CAAC,EAAEA,CAAC,GAAGc,aAAa,CAAC3B,MAAM,EAAEa,CAAC,EAAE,EAAE;QAC3C,MAAM;UAAEM,EAAE;UAAE4C,KAAK;UAAEC;QAAO,CAAC,GAAGrC,aAAa,CAACd,CAAC,CAAC;QAC9CQ,SAAS,CAAC4C,IAAI,CAACF,KAAK,CAAC;QACrBF,WAAW,CAACI,IAAI,CAAC7E,QAAQ,CAAC,CAAC,EAAEwB,aAAa,EAAEO,EAAE,CAAC,CAAC;QAChD2C,WAAW,CAACG,IAAI,CAACD,MAAM,IAAI,SAAS,CAAC;MACzC;MACA;AACZ;AACA;AACA;AACA;MACY,IAAIH,WAAW,CAAC,CAAC,CAAC,KAAK,CAAC,EAAE;QACtBA,WAAW,CAACZ,OAAO,CAAC,CAAC,CAAC;QACtB5B,SAAS,CAAC4B,OAAO,CAAC5B,SAAS,CAAC,CAAC,CAAC,CAAC;QAC/ByC,WAAW,CAACb,OAAO,CAACvD,oBAAoB,CAAC;MAC7C;MACA;AACZ;AACA;AACA;AACA;MACY,IAAImE,WAAW,CAACA,WAAW,CAAC7D,MAAM,GAAG,CAAC,CAAC,KAAK,CAAC,EAAE;QAC3C6D,WAAW,CAACI,IAAI,CAAC,CAAC,CAAC;QACnB5C,SAAS,CAAC4C,IAAI,CAAC,IAAI,CAAC;MACxB;MACA,IAAI,CAAC5D,oBAAoB,CAAC6D,GAAG,CAACX,OAAO,CAAC,EAAE;QACpClD,oBAAoB,CAACU,GAAG,CAACwC,OAAO,EAAE;UAC9BlC,SAAS,EAAE,CAAC,CAAC;UACbC,UAAU,EAAE,CAAC;QACjB,CAAC,CAAC;MACN;MACA,MAAM6C,UAAU,GAAG9D,oBAAoB,CAAC+D,GAAG,CAACb,OAAO,CAAC;MACpDY,UAAU,CAAC9C,SAAS,CAACmC,GAAG,CAAC,GAAGnC,SAAS;MACrC8C,UAAU,CAAC7C,UAAU,CAACkC,GAAG,CAAC,GAAG;QACzB,GAAG3D,iBAAiB;QACpBO,QAAQ,EAAEQ,aAAa;QACvBwB,IAAI,EAAE0B,WAAW;QACjB7B,KAAK,EAAE4B,WAAW;QAClB,GAAG/D;MACP,CAAC;IACL;EACJ,CAAC,CAAC;EACF,OAAOO,oBAAoB;AAC/B;AACA,SAAS+C,kBAAkBA,CAAChC,OAAO,EAAEb,SAAS,EAAE;EAC5C,CAACA,SAAS,CAAC2D,GAAG,CAAC9C,OAAO,CAAC,IAAIb,SAAS,CAACQ,GAAG,CAACK,OAAO,EAAE,CAAC,CAAC,CAAC;EACrD,OAAOb,SAAS,CAAC6D,GAAG,CAAChD,OAAO,CAAC;AACjC;AACA,SAASiC,gBAAgBA,CAACnC,IAAI,EAAEX,SAAS,EAAE;EACvC,IAAI,CAACA,SAAS,CAACW,IAAI,CAAC,EAChBX,SAAS,CAACW,IAAI,CAAC,GAAG,EAAE;EACxB,OAAOX,SAAS,CAACW,IAAI,CAAC;AAC1B;AACA,SAASa,eAAeA,CAACV,SAAS,EAAE;EAChC,OAAOL,KAAK,CAACC,OAAO,CAACI,SAAS,CAAC,GAAGA,SAAS,GAAG,CAACA,SAAS,CAAC;AAC7D;AACA,SAASoC,kBAAkBA,CAACnC,UAAU,EAAEkC,GAAG,EAAE;EACzC,OAAOlC,UAAU,CAACkC,GAAG,CAAC,GAChB;IAAE,GAAGlC,UAAU;IAAE,GAAGA,UAAU,CAACkC,GAAG;EAAE,CAAC,GACrC;IAAE,GAAGlC;EAAW,CAAC;AAC3B;AACA,MAAM+C,QAAQ,GAAIC,QAAQ,IAAK,OAAOA,QAAQ,KAAK,QAAQ;AAC3D,MAAM9B,sBAAsB,GAAInB,SAAS,IAAKA,SAAS,CAACkD,KAAK,CAACF,QAAQ,CAAC;AAEvE,SAAS1E,4BAA4B,EAAE8D,kBAAkB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}