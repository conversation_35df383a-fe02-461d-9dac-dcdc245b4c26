{"ast": null, "code": "import { mix } from '../../utils/mix.mjs';\nfunction calcLength(axis) {\n  return axis.max - axis.min;\n}\nfunction isNear(value) {\n  let target = arguments.length > 1 && arguments[1] !== undefined ? arguments[1] : 0;\n  let maxDistance = arguments.length > 2 && arguments[2] !== undefined ? arguments[2] : 0.01;\n  return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target) {\n  let origin = arguments.length > 3 && arguments[3] !== undefined ? arguments[3] : 0.5;\n  delta.origin = origin;\n  delta.originPoint = mix(source.min, source.max, delta.origin);\n  delta.scale = calcLength(target) / calcLength(source);\n  if (isNear(delta.scale, 1, 0.0001) || isNaN(delta.scale)) delta.scale = 1;\n  delta.translate = mix(target.min, target.max, delta.origin) - delta.originPoint;\n  if (isNear(delta.translate) || isNaN(delta.translate)) delta.translate = 0;\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n  calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n  calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n  target.min = parent.min + relative.min;\n  target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n  calcRelativeAxis(target.x, relative.x, parent.x);\n  calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n  target.min = layout.min - parent.min;\n  target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n  calcRelativeAxisPosition(target.x, layout.x, parent.x);\n  calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\nexport { calcAxisDelta, calcBoxDelta, calcLength, calcRelativeAxis, calcRelativeAxisPosition, calcRelativeBox, calcRelativePosition, isNear };", "map": {"version": 3, "names": ["mix", "calcLength", "axis", "max", "min", "isNear", "value", "target", "arguments", "length", "undefined", "maxDistance", "Math", "abs", "calcAxisDelta", "delta", "source", "origin", "originPoint", "scale", "isNaN", "translate", "calcBoxDelta", "x", "originX", "y", "originY", "calcRelativeAxis", "relative", "parent", "calcRelativeBox", "calcRelativeAxisPosition", "layout", "calcRelativePosition"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/projection/geometry/delta-calc.mjs"], "sourcesContent": ["import { mix } from '../../utils/mix.mjs';\n\nfunction calcLength(axis) {\n    return axis.max - axis.min;\n}\nfunction isNear(value, target = 0, maxDistance = 0.01) {\n    return Math.abs(value - target) <= maxDistance;\n}\nfunction calcAxisDelta(delta, source, target, origin = 0.5) {\n    delta.origin = origin;\n    delta.originPoint = mix(source.min, source.max, delta.origin);\n    delta.scale = calcLength(target) / calcLength(source);\n    if (isNear(delta.scale, 1, 0.0001) || isNaN(delta.scale))\n        delta.scale = 1;\n    delta.translate =\n        mix(target.min, target.max, delta.origin) - delta.originPoint;\n    if (isNear(delta.translate) || isNaN(delta.translate))\n        delta.translate = 0;\n}\nfunction calcBoxDelta(delta, source, target, origin) {\n    calcAxisDelta(delta.x, source.x, target.x, origin ? origin.originX : undefined);\n    calcAxisDelta(delta.y, source.y, target.y, origin ? origin.originY : undefined);\n}\nfunction calcRelativeAxis(target, relative, parent) {\n    target.min = parent.min + relative.min;\n    target.max = target.min + calcLength(relative);\n}\nfunction calcRelativeBox(target, relative, parent) {\n    calcRelativeAxis(target.x, relative.x, parent.x);\n    calcRelativeAxis(target.y, relative.y, parent.y);\n}\nfunction calcRelativeAxisPosition(target, layout, parent) {\n    target.min = layout.min - parent.min;\n    target.max = target.min + calcLength(layout);\n}\nfunction calcRelativePosition(target, layout, parent) {\n    calcRelativeAxisPosition(target.x, layout.x, parent.x);\n    calcRelativeAxisPosition(target.y, layout.y, parent.y);\n}\n\nexport { calcAxisDelta, calcBoxDelta, calcLength, calcRelativeAxis, calcRelativeAxisPosition, calcRelativeBox, calcRelativePosition, isNear };\n"], "mappings": "AAAA,SAASA,GAAG,QAAQ,qBAAqB;AAEzC,SAASC,UAAUA,CAACC,IAAI,EAAE;EACtB,OAAOA,IAAI,CAACC,GAAG,GAAGD,IAAI,CAACE,GAAG;AAC9B;AACA,SAASC,MAAMA,CAACC,KAAK,EAAkC;EAAA,IAAhCC,MAAM,GAAAC,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,CAAC;EAAA,IAAEG,WAAW,GAAAH,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,IAAI;EACjD,OAAOI,IAAI,CAACC,GAAG,CAACP,KAAK,GAAGC,MAAM,CAAC,IAAII,WAAW;AAClD;AACA,SAASG,aAAaA,CAACC,KAAK,EAAEC,MAAM,EAAET,MAAM,EAAgB;EAAA,IAAdU,MAAM,GAAAT,SAAA,CAAAC,MAAA,QAAAD,SAAA,QAAAE,SAAA,GAAAF,SAAA,MAAG,GAAG;EACtDO,KAAK,CAACE,MAAM,GAAGA,MAAM;EACrBF,KAAK,CAACG,WAAW,GAAGlB,GAAG,CAACgB,MAAM,CAACZ,GAAG,EAAEY,MAAM,CAACb,GAAG,EAAEY,KAAK,CAACE,MAAM,CAAC;EAC7DF,KAAK,CAACI,KAAK,GAAGlB,UAAU,CAACM,MAAM,CAAC,GAAGN,UAAU,CAACe,MAAM,CAAC;EACrD,IAAIX,MAAM,CAACU,KAAK,CAACI,KAAK,EAAE,CAAC,EAAE,MAAM,CAAC,IAAIC,KAAK,CAACL,KAAK,CAACI,KAAK,CAAC,EACpDJ,KAAK,CAACI,KAAK,GAAG,CAAC;EACnBJ,KAAK,CAACM,SAAS,GACXrB,GAAG,CAACO,MAAM,CAACH,GAAG,EAAEG,MAAM,CAACJ,GAAG,EAAEY,KAAK,CAACE,MAAM,CAAC,GAAGF,KAAK,CAACG,WAAW;EACjE,IAAIb,MAAM,CAACU,KAAK,CAACM,SAAS,CAAC,IAAID,KAAK,CAACL,KAAK,CAACM,SAAS,CAAC,EACjDN,KAAK,CAACM,SAAS,GAAG,CAAC;AAC3B;AACA,SAASC,YAAYA,CAACP,KAAK,EAAEC,MAAM,EAAET,MAAM,EAAEU,MAAM,EAAE;EACjDH,aAAa,CAACC,KAAK,CAACQ,CAAC,EAAEP,MAAM,CAACO,CAAC,EAAEhB,MAAM,CAACgB,CAAC,EAAEN,MAAM,GAAGA,MAAM,CAACO,OAAO,GAAGd,SAAS,CAAC;EAC/EI,aAAa,CAACC,KAAK,CAACU,CAAC,EAAET,MAAM,CAACS,CAAC,EAAElB,MAAM,CAACkB,CAAC,EAAER,MAAM,GAAGA,MAAM,CAACS,OAAO,GAAGhB,SAAS,CAAC;AACnF;AACA,SAASiB,gBAAgBA,CAACpB,MAAM,EAAEqB,QAAQ,EAAEC,MAAM,EAAE;EAChDtB,MAAM,CAACH,GAAG,GAAGyB,MAAM,CAACzB,GAAG,GAAGwB,QAAQ,CAACxB,GAAG;EACtCG,MAAM,CAACJ,GAAG,GAAGI,MAAM,CAACH,GAAG,GAAGH,UAAU,CAAC2B,QAAQ,CAAC;AAClD;AACA,SAASE,eAAeA,CAACvB,MAAM,EAAEqB,QAAQ,EAAEC,MAAM,EAAE;EAC/CF,gBAAgB,CAACpB,MAAM,CAACgB,CAAC,EAAEK,QAAQ,CAACL,CAAC,EAAEM,MAAM,CAACN,CAAC,CAAC;EAChDI,gBAAgB,CAACpB,MAAM,CAACkB,CAAC,EAAEG,QAAQ,CAACH,CAAC,EAAEI,MAAM,CAACJ,CAAC,CAAC;AACpD;AACA,SAASM,wBAAwBA,CAACxB,MAAM,EAAEyB,MAAM,EAAEH,MAAM,EAAE;EACtDtB,MAAM,CAACH,GAAG,GAAG4B,MAAM,CAAC5B,GAAG,GAAGyB,MAAM,CAACzB,GAAG;EACpCG,MAAM,CAACJ,GAAG,GAAGI,MAAM,CAACH,GAAG,GAAGH,UAAU,CAAC+B,MAAM,CAAC;AAChD;AACA,SAASC,oBAAoBA,CAAC1B,MAAM,EAAEyB,MAAM,EAAEH,MAAM,EAAE;EAClDE,wBAAwB,CAACxB,MAAM,CAACgB,CAAC,EAAES,MAAM,CAACT,CAAC,EAAEM,MAAM,CAACN,CAAC,CAAC;EACtDQ,wBAAwB,CAACxB,MAAM,CAACkB,CAAC,EAAEO,MAAM,CAACP,CAAC,EAAEI,MAAM,CAACJ,CAAC,CAAC;AAC1D;AAEA,SAASX,aAAa,EAAEQ,YAAY,EAAErB,UAAU,EAAE0B,gBAAgB,EAAEI,wBAAwB,EAAED,eAAe,EAAEG,oBAAoB,EAAE5B,MAAM"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}