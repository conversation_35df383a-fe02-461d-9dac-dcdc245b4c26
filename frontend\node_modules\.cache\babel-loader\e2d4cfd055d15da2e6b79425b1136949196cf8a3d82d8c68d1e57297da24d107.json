{"ast": null, "code": "const appearStoreId = (id, value) => `${id}: ${value}`;\nexport { appearStoreId };", "map": {"version": 3, "names": ["appearStoreId", "id", "value"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/animation/optimized-appear/store-id.mjs"], "sourcesContent": ["const appearStoreId = (id, value) => `${id}: ${value}`;\n\nexport { appearStoreId };\n"], "mappings": "AAAA,MAAMA,aAAa,GAAGA,CAACC,EAAE,EAAEC,KAAK,KAAM,GAAED,EAAG,KAAIC,KAAM,EAAC;AAEtD,SAASF,aAAa"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}