{"ast": null, "code": "import { HTMLVisualElement } from '../html/HTMLVisualElement.mjs';\nimport { SVGVisualElement } from '../svg/SVGVisualElement.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\nconst createDomVisualElement = (Component, options) => {\n  return isSVGComponent(Component) ? new SVGVisualElement(options, {\n    enableHardwareAcceleration: false\n  }) : new HTMLVisualElement(options, {\n    enableHardwareAcceleration: true\n  });\n};\nexport { createDomVisualElement };", "map": {"version": 3, "names": ["HTMLVisualElement", "SVGVisualElement", "isSVGComponent", "createDomVisualElement", "Component", "options", "enableHardwareAcceleration"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/framer-motion/dist/es/render/dom/create-visual-element.mjs"], "sourcesContent": ["import { HTMLVisualElement } from '../html/HTMLVisualElement.mjs';\nimport { SVGVisualElement } from '../svg/SVGVisualElement.mjs';\nimport { isSVGComponent } from './utils/is-svg-component.mjs';\n\nconst createDomVisualElement = (Component, options) => {\n    return isSVGComponent(Component)\n        ? new SVGVisualElement(options, { enableHardwareAcceleration: false })\n        : new HTMLVisualElement(options, { enableHardwareAcceleration: true });\n};\n\nexport { createDomVisualElement };\n"], "mappings": "AAAA,SAASA,iBAAiB,QAAQ,+BAA+B;AACjE,SAASC,gBAAgB,QAAQ,6BAA6B;AAC9D,SAASC,cAAc,QAAQ,8BAA8B;AAE7D,MAAMC,sBAAsB,GAAGA,CAACC,SAAS,EAAEC,OAAO,KAAK;EACnD,OAAOH,cAAc,CAACE,SAAS,CAAC,GAC1B,IAAIH,gBAAgB,CAACI,OAAO,EAAE;IAAEC,0BAA0B,EAAE;EAAM,CAAC,CAAC,GACpE,IAAIN,iBAAiB,CAACK,OAAO,EAAE;IAAEC,0BAA0B,EAAE;EAAK,CAAC,CAAC;AAC9E,CAAC;AAED,SAASH,sBAAsB"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}