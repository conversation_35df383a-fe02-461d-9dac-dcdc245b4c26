{"ast": null, "code": "export default function uniqueBy(arr, fn) {\n  var identifiers = new Set();\n  return arr.filter(function (item) {\n    var identifier = fn(item);\n    if (!identifiers.has(identifier)) {\n      identifiers.add(identifier);\n      return true;\n    }\n  });\n}", "map": {"version": 3, "names": ["uniqueBy", "arr", "fn", "identifiers", "Set", "filter", "item", "identifier", "has", "add"], "sources": ["D:/MINHNGUYET/NAM4/DoAn/Python-KienTap-/frontend/node_modules/@popperjs/core/lib/utils/uniqueBy.js"], "sourcesContent": ["export default function uniqueBy(arr, fn) {\n  var identifiers = new Set();\n  return arr.filter(function (item) {\n    var identifier = fn(item);\n\n    if (!identifiers.has(identifier)) {\n      identifiers.add(identifier);\n      return true;\n    }\n  });\n}"], "mappings": "AAAA,eAAe,SAASA,QAAQA,CAACC,GAAG,EAAEC,EAAE,EAAE;EACxC,IAAIC,WAAW,GAAG,IAAIC,GAAG,EAAE;EAC3B,OAAOH,GAAG,CAACI,MAAM,CAAC,UAAUC,IAAI,EAAE;IAChC,IAAIC,UAAU,GAAGL,EAAE,CAACI,IAAI,CAAC;IAEzB,IAAI,CAACH,WAAW,CAACK,GAAG,CAACD,UAAU,CAAC,EAAE;MAChCJ,WAAW,CAACM,GAAG,CAACF,UAAU,CAAC;MAC3B,OAAO,IAAI;IACb;EACF,CAAC,CAAC;AACJ"}, "metadata": {}, "sourceType": "module", "externalDependencies": []}